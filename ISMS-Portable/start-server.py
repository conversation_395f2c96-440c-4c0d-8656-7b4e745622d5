#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خادم HTTP بسيط لتشغيل نظام إدارة أمن المعلومات
Simple HTTP Server for Information Security Management System
"""

import http.server
import socketserver
import webbrowser
import socket
import sys
import os
from urllib.parse import urlparse

class CustomHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=os.getcwd(), **kwargs)
    
    def end_headers(self):
        # إضافة headers للأمان
        self.send_header('X-Frame-Options', 'SAMEORIGIN')
        self.send_header('X-XSS-Protection', '1; mode=block')
        self.send_header('X-Content-Type-Options', 'nosniff')
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        super().end_headers()
    
    def do_GET(self):
        # إعادة توجيه الصفحة الرئيسية إلى login.html فقط إذا لم يكن هناك session
        if self.path == '/':
            self.send_response(301)
            self.send_header('Location', '/login.html')
            self.end_headers()
            return

        super().do_GET()

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        # الاتصال بخادم خارجي للحصول على IP المحلي
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "127.0.0.1"

def find_free_port(start_port=8000):
    """البحث عن منفذ متاح"""
    for port in range(start_port, start_port + 100):
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('', port))
                return port
        except OSError:
            continue
    return None

def main():
    # البحث عن منفذ متاح
    port = find_free_port(8000)
    if not port:
        print("❌ لا يمكن العثور على منفذ متاح")
        sys.exit(1)
    
    # الحصول على عنوان IP المحلي
    local_ip = get_local_ip()
    
    # إنشاء الخادم
    try:
        with socketserver.TCPServer(("", port), CustomHTTPRequestHandler) as httpd:
            print("🔐 نظام إدارة أمن المعلومات")
            print("=" * 50)
            print(f"🌐 الخادم يعمل على:")
            print(f"   📍 المحلي: http://localhost:{port}/login.html")
            print(f"   📍 الشبكة: http://{local_ip}:{port}/login.html")
            print("=" * 50)
            print("🔑 بيانات الدخول الافتراضية:")
            print("   👤 اسم المستخدم: admin")
            print("   🔒 كلمة المرور: admin123")
            print("=" * 50)
            print("⚠️  للإيقاف: اضغط Ctrl+C")
            print()
            
            # فتح المتصفح تلقائياً
            try:
                webbrowser.open(f'http://localhost:{port}/login.html')
                print("🌐 تم فتح المتصفح تلقائياً")
            except:
                print("⚠️  يرجى فتح المتصفح يدوياً")
            
            print()
            print("🚀 الخادم جاهز...")
            
            # تشغيل الخادم
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف الخادم")
    except Exception as e:
        print(f"❌ خطأ في تشغيل الخادم: {e}")

if __name__ == "__main__":
    main()
