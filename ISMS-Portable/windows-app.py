#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق ويندوز لنظام إدارة أمن المعلومات
Windows Application for Information Security Management System
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import webbrowser
import threading
import subprocess
import sys
import os
import json
from datetime import datetime
import socket

class ISMSWindowsApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام إدارة أمن المعلومات - Information Security Management System")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # تعيين الأيقونة إذا كانت متوفرة
        try:
            self.root.iconbitmap('cyber-security.ico')
        except:
            pass
        
        self.server_process = None
        self.server_running = False
        self.current_port = 8000
        self.local_ip = self.get_local_ip()
        
        self.setup_ui()
        self.load_settings()
        
    def get_local_ip(self):
        """الحصول على عنوان IP المحلي"""
        try:
            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            s.connect(("*******", 80))
            local_ip = s.getsockname()[0]
            s.close()
            return local_ip
        except:
            return "127.0.0.1"
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#2563eb', height=80)
        title_frame.pack(fill='x', padx=10, pady=10)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame, 
            text="🔐 نظام إدارة أمن المعلومات",
            font=('Arial', 16, 'bold'),
            fg='white',
            bg='#2563eb'
        )
        title_label.pack(expand=True)
        
        # معلومات الخادم
        info_frame = tk.LabelFrame(self.root, text="معلومات الخادم", font=('Arial', 12, 'bold'))
        info_frame.pack(fill='x', padx=10, pady=5)
        
        # حالة الخادم
        self.status_label = tk.Label(
            info_frame, 
            text="🔴 الخادم متوقف",
            font=('Arial', 11),
            fg='red'
        )
        self.status_label.pack(pady=5)
        
        # عنوان IP
        self.ip_label = tk.Label(
            info_frame,
            text=f"📍 عنوان IP: {self.local_ip}",
            font=('Arial', 10)
        )
        self.ip_label.pack()
        
        # المنفذ
        port_frame = tk.Frame(info_frame)
        port_frame.pack(pady=5)
        
        tk.Label(port_frame, text="المنفذ:", font=('Arial', 10)).pack(side='left')
        self.port_var = tk.StringVar(value=str(self.current_port))
        self.port_entry = tk.Entry(port_frame, textvariable=self.port_var, width=10)
        self.port_entry.pack(side='left', padx=5)
        
        # أزرار التحكم
        control_frame = tk.LabelFrame(self.root, text="التحكم في الخادم", font=('Arial', 12, 'bold'))
        control_frame.pack(fill='x', padx=10, pady=5)
        
        buttons_frame = tk.Frame(control_frame)
        buttons_frame.pack(pady=10)
        
        self.start_btn = tk.Button(
            buttons_frame,
            text="🚀 تشغيل الخادم",
            command=self.start_server,
            bg='#10b981',
            fg='white',
            font=('Arial', 11, 'bold'),
            width=15
        )
        self.start_btn.pack(side='left', padx=5)
        
        self.stop_btn = tk.Button(
            buttons_frame,
            text="⏹️ إيقاف الخادم",
            command=self.stop_server,
            bg='#ef4444',
            fg='white',
            font=('Arial', 11, 'bold'),
            width=15,
            state='disabled'
        )
        self.stop_btn.pack(side='left', padx=5)
        
        self.browser_btn = tk.Button(
            buttons_frame,
            text="🌐 فتح المتصفح",
            command=self.open_browser,
            bg='#3b82f6',
            fg='white',
            font=('Arial', 11, 'bold'),
            width=15,
            state='disabled'
        )
        self.browser_btn.pack(side='left', padx=5)
        
        # روابط الوصول
        links_frame = tk.LabelFrame(self.root, text="روابط الوصول", font=('Arial', 12, 'bold'))
        links_frame.pack(fill='x', padx=10, pady=5)
        
        self.local_link_var = tk.StringVar(value="http://localhost:8000/login.html")
        self.network_link_var = tk.StringVar(value=f"http://{self.local_ip}:8000/login.html")
        
        # الرابط المحلي
        local_frame = tk.Frame(links_frame)
        local_frame.pack(fill='x', pady=2)
        tk.Label(local_frame, text="المحلي:", font=('Arial', 10, 'bold')).pack(side='left')
        local_entry = tk.Entry(local_frame, textvariable=self.local_link_var, state='readonly')
        local_entry.pack(side='left', fill='x', expand=True, padx=5)
        tk.Button(local_frame, text="نسخ", command=lambda: self.copy_to_clipboard(self.local_link_var.get())).pack(side='right')
        
        # رابط الشبكة
        network_frame = tk.Frame(links_frame)
        network_frame.pack(fill='x', pady=2)
        tk.Label(network_frame, text="الشبكة:", font=('Arial', 10, 'bold')).pack(side='left')
        network_entry = tk.Entry(network_frame, textvariable=self.network_link_var, state='readonly')
        network_entry.pack(side='left', fill='x', expand=True, padx=5)
        tk.Button(network_frame, text="نسخ", command=lambda: self.copy_to_clipboard(self.network_link_var.get())).pack(side='right')
        
        # بيانات تسجيل الدخول
        login_frame = tk.LabelFrame(self.root, text="بيانات تسجيل الدخول", font=('Arial', 12, 'bold'))
        login_frame.pack(fill='x', padx=10, pady=5)
        
        login_info = [
            ("المدير الرئيسي:", "admin / admin123"),
            ("المحلل الأمني:", "analyst / analyst123"),
            ("مشغل النظام:", "operator / operator123")
        ]
        
        for title, credentials in login_info:
            frame = tk.Frame(login_frame)
            frame.pack(fill='x', pady=1)
            tk.Label(frame, text=title, font=('Arial', 10, 'bold')).pack(side='left')
            tk.Label(frame, text=credentials, font=('Arial', 10)).pack(side='left', padx=10)
        
        # سجل الأحداث
        log_frame = tk.LabelFrame(self.root, text="سجل الأحداث", font=('Arial', 12, 'bold'))
        log_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # إنشاء Text widget مع scrollbar
        log_text_frame = tk.Frame(log_frame)
        log_text_frame.pack(fill='both', expand=True, padx=5, pady=5)
        
        self.log_text = tk.Text(log_text_frame, height=8, wrap='word')
        scrollbar = tk.Scrollbar(log_text_frame, orient='vertical', command=self.log_text.yview)
        self.log_text.configure(yscrollcommand=scrollbar.set)
        
        self.log_text.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # شريط الحالة
        self.status_bar = tk.Label(
            self.root,
            text="جاهز للتشغيل",
            relief='sunken',
            anchor='w'
        )
        self.status_bar.pack(side='bottom', fill='x')
        
        # إضافة رسالة ترحيب
        self.log_message("🔐 مرحباً بك في نظام إدارة أمن المعلومات")
        self.log_message("📋 اضغط 'تشغيل الخادم' للبدء")
    
    def log_message(self, message):
        """إضافة رسالة لسجل الأحداث"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert('end', log_entry)
        self.log_text.see('end')
        self.root.update()
    
    def copy_to_clipboard(self, text):
        """نسخ النص للحافظة"""
        self.root.clipboard_clear()
        self.root.clipboard_append(text)
        messagebox.showinfo("تم", "تم نسخ الرابط للحافظة")
    
    def find_available_port(self, start_port=8000):
        """البحث عن منفذ متاح"""
        for port in range(start_port, start_port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('', port))
                    return port
            except OSError:
                continue
        return None
    
    def start_server(self):
        """تشغيل الخادم"""
        try:
            port = int(self.port_var.get())
        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال رقم منفذ صحيح")
            return
        
        # التحقق من توفر المنفذ
        available_port = self.find_available_port(port)
        if available_port != port:
            if available_port:
                response = messagebox.askyesno(
                    "المنفذ مستخدم", 
                    f"المنفذ {port} مستخدم. هل تريد استخدام المنفذ {available_port}؟"
                )
                if response:
                    port = available_port
                    self.port_var.set(str(port))
                else:
                    return
            else:
                messagebox.showerror("خطأ", "لا يمكن العثور على منفذ متاح")
                return
        
        self.current_port = port
        self.update_links()
        
        # تشغيل الخادم في thread منفصل
        def run_server():
            try:
                cmd = [sys.executable, "start-server.py"]
                self.server_process = subprocess.Popen(
                    cmd,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    cwd=os.getcwd()
                )
                
                self.server_running = True
                self.root.after(0, self.update_ui_server_started)
                
                # قراءة output الخادم
                for line in iter(self.server_process.stdout.readline, ''):
                    if line.strip():
                        self.root.after(0, lambda msg=line.strip(): self.log_message(msg))
                
            except Exception as e:
                self.root.after(0, lambda: self.log_message(f"❌ خطأ في تشغيل الخادم: {e}"))
                self.server_running = False
                self.root.after(0, self.update_ui_server_stopped)
        
        threading.Thread(target=run_server, daemon=True).start()
        self.log_message("🚀 جاري تشغيل الخادم...")
    
    def stop_server(self):
        """إيقاف الخادم"""
        if self.server_process:
            self.server_process.terminate()
            self.server_process = None
        
        self.server_running = False
        self.update_ui_server_stopped()
        self.log_message("⏹️ تم إيقاف الخادم")
    
    def update_ui_server_started(self):
        """تحديث واجهة المستخدم عند تشغيل الخادم"""
        self.status_label.config(text="🟢 الخادم يعمل", fg='green')
        self.start_btn.config(state='disabled')
        self.stop_btn.config(state='normal')
        self.browser_btn.config(state='normal')
        self.status_bar.config(text=f"الخادم يعمل على المنفذ {self.current_port}")
    
    def update_ui_server_stopped(self):
        """تحديث واجهة المستخدم عند إيقاف الخادم"""
        self.status_label.config(text="🔴 الخادم متوقف", fg='red')
        self.start_btn.config(state='normal')
        self.stop_btn.config(state='disabled')
        self.browser_btn.config(state='disabled')
        self.status_bar.config(text="الخادم متوقف")
    
    def update_links(self):
        """تحديث روابط الوصول"""
        self.local_link_var.set(f"http://localhost:{self.current_port}/login.html")
        self.network_link_var.set(f"http://{self.local_ip}:{self.current_port}/login.html")
    
    def open_browser(self):
        """فتح المتصفح"""
        if self.server_running:
            url = f"http://localhost:{self.current_port}/login.html"
            webbrowser.open(url)
            self.log_message(f"🌐 تم فتح المتصفح: {url}")
        else:
            messagebox.showwarning("تحذير", "يجب تشغيل الخادم أولاً")
    
    def load_settings(self):
        """تحميل الإعدادات"""
        try:
            if os.path.exists('windows-app-settings.json'):
                with open('windows-app-settings.json', 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    self.port_var.set(str(settings.get('port', 8000)))
        except:
            pass
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            settings = {
                'port': int(self.port_var.get()),
                'last_used': datetime.now().isoformat()
            }
            with open('windows-app-settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)
        except:
            pass
    
    def on_closing(self):
        """عند إغلاق التطبيق"""
        if self.server_running:
            response = messagebox.askyesno("تأكيد الإغلاق", "الخادم يعمل. هل تريد إيقافه وإغلاق التطبيق؟")
            if response:
                self.stop_server()
            else:
                return
        
        self.save_settings()
        self.root.destroy()
    
    def run(self):
        """تشغيل التطبيق"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

if __name__ == "__main__":
    app = ISMSWindowsApp()
    app.run()
