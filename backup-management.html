<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إدارة النسخ الاحتياطية - نظام إدارة أمن المعلومات</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <!-- SheetJS for Excel export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
        }
        
        .header-section {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: none;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .action-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 5px solid #4CAF50;
        }
        
        .btn-custom {
            border-radius: 25px;
            padding: 12px 30px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            transition: all 0.3s ease;
            border: none;
            margin: 5px;
        }
        
        .btn-export {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
        }
        
        .btn-export:hover {
            background: linear-gradient(135deg, #218838 0%, #1a9b7a 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }
        
        .btn-import {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
            color: white;
        }
        
        .btn-import:hover {
            background: linear-gradient(135deg, #0056b3 0%, #520dc2 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 123, 255, 0.4);
        }
        
        .btn-danger-custom {
            background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
            color: white;
        }
        
        .btn-danger-custom:hover {
            background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(220, 53, 69, 0.4);
        }
        
        .backup-item {
            background: white;
            border-radius: 10px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #4CAF50;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        
        .backup-item:hover {
            transform: translateX(5px);
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
        }
        
        .progress-custom {
            height: 25px;
            border-radius: 15px;
            background: #e9ecef;
            overflow: hidden;
        }
        
        .progress-bar-custom {
            background: linear-gradient(90deg, #4CAF50 0%, #45a049 100%);
            border-radius: 15px;
            transition: width 0.6s ease;
        }
        
        .modal-custom .modal-content {
            border-radius: 20px;
            border: none;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }
        
        .modal-custom .modal-header {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
            border-radius: 20px 20px 0 0;
            border-bottom: none;
        }
        
        .file-drop-zone {
            border: 3px dashed #4CAF50;
            border-radius: 15px;
            padding: 40px;
            text-align: center;
            background: #f8f9fa;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .file-drop-zone:hover {
            background: #e9ecef;
            border-color: #45a049;
        }
        
        .file-drop-zone.dragover {
            background: #d4edda;
            border-color: #28a745;
        }
        
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .table-custom {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .table-custom thead {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
            color: white;
        }
        
        .table-custom tbody tr:hover {
            background: #f8f9fa;
        }
        
        .icon-large {
            font-size: 3rem;
            margin-bottom: 15px;
            color: #4CAF50;
        }
        
        .nav-pills-custom .nav-link {
            border-radius: 25px;
            margin: 0 5px;
            font-weight: 600;
        }
        
        .nav-pills-custom .nav-link.active {
            background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header Section -->
            <div class="header-section">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <div>
                        <h1><i class="fas fa-database"></i> إدارة النسخ الاحتياطية</h1>
                        <p class="mb-0">نظام شامل لإدارة وحماية بيانات النظام</p>
                    </div>
                    <div>
                        <button class="btn btn-light btn-custom" onclick="goToMainSystem()" title="العودة للنظام الرئيسي">
                            <i class="fas fa-home"></i> النظام الرئيسي
                        </button>
                        <button class="btn btn-outline-light btn-custom" onclick="goToLogin()" title="تسجيل الخروج">
                            <i class="fas fa-sign-out-alt"></i> خروج
                        </button>
                    </div>
                </div>
            </div>

            <!-- Navigation Tabs -->
            <ul class="nav nav-pills nav-pills-custom justify-content-center mb-4" id="backupTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="overview-tab" data-bs-toggle="pill" data-bs-target="#overview" type="button" role="tab">
                        <i class="fas fa-chart-pie"></i> نظرة عامة
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="export-tab" data-bs-toggle="pill" data-bs-target="#export" type="button" role="tab">
                        <i class="fas fa-download"></i> التصدير
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="import-tab" data-bs-toggle="pill" data-bs-target="#import" type="button" role="tab">
                        <i class="fas fa-upload"></i> الاستيراد
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="manage-tab" data-bs-toggle="pill" data-bs-target="#manage" type="button" role="tab">
                        <i class="fas fa-cogs"></i> الإدارة
                    </button>
                </li>
            </ul>

            <!-- Tab Content -->
            <div class="tab-content" id="backupTabsContent">
                <!-- Overview Tab -->
                <div class="tab-pane fade show active" id="overview" role="tabpanel">
                    <div class="row">
                        <!-- Statistics Cards -->
                        <div class="col-md-3 mb-4">
                            <div class="stats-card text-center">
                                <i class="fas fa-database icon-large"></i>
                                <h3 id="totalBackups">0</h3>
                                <p>إجمالي النسخ</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="stats-card text-center">
                                <i class="fas fa-clock icon-large"></i>
                                <h3 id="lastBackupTime">--</h3>
                                <p>آخر نسخة</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="stats-card text-center">
                                <i class="fas fa-hdd icon-large"></i>
                                <h3 id="totalSize">0 KB</h3>
                                <p>الحجم الإجمالي</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-4">
                            <div class="stats-card text-center">
                                <i class="fas fa-shield-alt icon-large"></i>
                                <h3 id="systemHealth">ممتاز</h3>
                                <p>حالة النظام</p>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="action-card">
                                <h4><i class="fas fa-download"></i> إنشاء نسخة احتياطية سريعة</h4>
                                <p>إنشاء نسخة احتياطية فورية من جميع بيانات النظام</p>
                                <button class="btn btn-export btn-custom" onclick="createQuickBackup()">
                                    <i class="fas fa-rocket"></i> إنشاء الآن
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="action-card">
                                <h4><i class="fas fa-upload"></i> استيراد نسخة احتياطية</h4>
                                <p>استيراد واستعادة البيانات من نسخة احتياطية محفوظة</p>
                                <button class="btn btn-import btn-custom" onclick="showImportModal()">
                                    <i class="fas fa-file-import"></i> استيراد
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Export Tab -->
                <div class="tab-pane fade" id="export" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="action-card">
                                <h4><i class="fas fa-file-export"></i> تصدير البيانات</h4>
                                <p>اختر نوع البيانات وتنسيق التصدير المطلوب</p>

                                <!-- Data Selection -->
                                <div class="row mb-3">
                                    <div class="col-md-6">
                                        <h6>اختيار البيانات:</h6>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="exportUsers" checked>
                                            <label class="form-check-label" for="exportUsers">المستخدمين</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="exportEvents" checked>
                                            <label class="form-check-label" for="exportEvents">الأحداث الأمنية</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="exportActivities" checked>
                                            <label class="form-check-label" for="exportActivities">سجل الأنشطة</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="exportSettings" checked>
                                            <label class="form-check-label" for="exportSettings">إعدادات النظام</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h6>تنسيق التصدير:</h6>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="exportFormat" id="formatJSON" value="json" checked>
                                            <label class="form-check-label" for="formatJSON">JSON (موصى به)</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="exportFormat" id="formatExcel" value="excel">
                                            <label class="form-check-label" for="formatExcel">Excel (.xlsx)</label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="exportFormat" id="formatCSV" value="csv">
                                            <label class="form-check-label" for="formatCSV">CSV</label>
                                        </div>
                                    </div>
                                </div>

                                <!-- Export Options -->
                                <div class="mb-3">
                                    <h6>خيارات إضافية:</h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="includeMetadata" checked>
                                        <label class="form-check-label" for="includeMetadata">تضمين البيانات الوصفية</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="compressFile">
                                        <label class="form-check-label" for="compressFile">ضغط الملف</label>
                                    </div>
                                </div>

                                <!-- Export Buttons -->
                                <div class="text-center">
                                    <button class="btn btn-export btn-custom" onclick="exportData()">
                                        <i class="fas fa-download"></i> تصدير البيانات
                                    </button>
                                    <button class="btn btn-export btn-custom" onclick="scheduleExport()">
                                        <i class="fas fa-clock"></i> جدولة التصدير
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="action-card">
                                <h5><i class="fas fa-info-circle"></i> معلومات التصدير</h5>
                                <div id="exportInfo">
                                    <p><strong>البيانات المحددة:</strong> <span id="selectedDataCount">4</span> أنواع</p>
                                    <p><strong>الحجم المتوقع:</strong> <span id="estimatedSize">تحديد...</span></p>
                                    <p><strong>وقت التصدير:</strong> <span id="exportTime">~5 ثوان</span></p>
                                </div>

                                <hr>

                                <h6><i class="fas fa-history"></i> آخر العمليات</h6>
                                <div id="recentExports">
                                    <small class="text-muted">لا توجد عمليات تصدير حديثة</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Import Tab -->
                <div class="tab-pane fade" id="import" role="tabpanel">
                    <div class="row">
                        <div class="col-md-8">
                            <div class="action-card">
                                <h4><i class="fas fa-file-import"></i> استيراد البيانات</h4>
                                <p>اختر ملف النسخة الاحتياطية لاستيراد البيانات</p>

                                <!-- File Drop Zone -->
                                <div class="file-drop-zone" id="fileDropZone" onclick="document.getElementById('importFile').click()">
                                    <i class="fas fa-cloud-upload-alt" style="font-size: 3rem; color: #4CAF50; margin-bottom: 15px;"></i>
                                    <h5>اسحب الملف هنا أو انقر للاختيار</h5>
                                    <p class="text-muted">يدعم ملفات JSON, Excel, CSV</p>
                                    <input type="file" id="importFile" accept=".json,.xlsx,.csv" style="display: none;" onchange="handleFileSelect(this)">
                                </div>

                                <!-- Import Progress -->
                                <div id="importProgress" style="display: none;">
                                    <div class="progress-custom mt-3">
                                        <div class="progress-bar-custom" id="importProgressBar" style="width: 0%"></div>
                                    </div>
                                    <p class="text-center mt-2" id="importStatus">جاري المعالجة...</p>
                                </div>

                                <!-- Import Options -->
                                <div id="importOptions" style="display: none;">
                                    <hr>
                                    <h6>خيارات الاستيراد:</h6>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="importMode" id="mergeData" value="merge" checked>
                                        <label class="form-check-label" for="mergeData">دمج مع البيانات الموجودة</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="importMode" id="replaceData" value="replace">
                                        <label class="form-check-label" for="replaceData">استبدال البيانات الموجودة</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="createBackupBeforeImport" checked>
                                        <label class="form-check-label" for="createBackupBeforeImport">إنشاء نسخة احتياطية قبل الاستيراد</label>
                                    </div>

                                    <div class="text-center mt-3">
                                        <button class="btn btn-import btn-custom" onclick="confirmImport()">
                                            <i class="fas fa-check"></i> تأكيد الاستيراد
                                        </button>
                                        <button class="btn btn-secondary btn-custom" onclick="cancelImport()">
                                            <i class="fas fa-times"></i> إلغاء
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-4">
                            <div class="action-card">
                                <h5><i class="fas fa-file-alt"></i> معاينة الملف</h5>
                                <div id="filePreview">
                                    <p class="text-muted text-center">لم يتم اختيار ملف</p>
                                </div>

                                <hr>

                                <h6><i class="fas fa-exclamation-triangle"></i> تحذيرات</h6>
                                <div class="alert alert-warning">
                                    <small>
                                        • تأكد من صحة الملف قبل الاستيراد<br>
                                        • سيتم إنشاء نسخة احتياطية تلقائياً<br>
                                        • قد تستغرق العملية عدة دقائق
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Management Tab -->
                <div class="tab-pane fade" id="manage" role="tabpanel">
                    <div class="action-card">
                        <h4><i class="fas fa-cogs"></i> إدارة النسخ الاحتياطية</h4>
                        <p>عرض وإدارة جميع النسخ الاحتياطية المحفوظة</p>

                        <!-- Search and Filter -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <input type="text" class="form-control" id="searchBackups" placeholder="البحث في النسخ الاحتياطية...">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select" id="filterType">
                                    <option value="">جميع الأنواع</option>
                                    <option value="manual">يدوي</option>
                                    <option value="automatic">تلقائي</option>
                                    <option value="scheduled">مجدول</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-export btn-custom" onclick="refreshBackupList()">
                                    <i class="fas fa-sync"></i> تحديث
                                </button>
                            </div>
                        </div>

                        <!-- Backup List -->
                        <div class="table-responsive">
                            <table class="table table-custom">
                                <thead>
                                    <tr>
                                        <th>اسم الملف</th>
                                        <th>التاريخ</th>
                                        <th>الحجم</th>
                                        <th>النوع</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="backupTableBody">
                                    <tr>
                                        <td colspan="6" class="text-center text-muted">جاري تحميل البيانات...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modals -->
    <!-- Import Confirmation Modal -->
    <div class="modal fade modal-custom" id="importModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title"><i class="fas fa-upload"></i> تأكيد الاستيراد</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="importConfirmationContent">
                        <!-- Content will be populated by JavaScript -->
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="button" class="btn btn-import btn-custom" onclick="executeImport()">تأكيد الاستيراد</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Global variables
        let currentImportData = null;
        let backupList = [];

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializePage();
            loadBackupStatistics();
            loadBackupList();
            setupEventListeners();
        });

        function initializePage() {
            console.log('🚀 تهيئة صفحة إدارة النسخ الاحتياطية');

            // Check if we're running in the ISMS system
            if (typeof logActivity === 'function') {
                logActivity('backup_page_accessed', 'تم الوصول لصفحة إدارة النسخ الاحتياطية');
            }

            // Setup drag and drop
            setupDragAndDrop();
        }

        function setupEventListeners() {
            // Export data selection change
            document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
                if (checkbox.id.startsWith('export')) {
                    checkbox.addEventListener('change', updateExportInfo);
                }
            });

            // Search functionality
            document.getElementById('searchBackups').addEventListener('input', filterBackups);
            document.getElementById('filterType').addEventListener('change', filterBackups);
        }

        function setupDragAndDrop() {
            const dropZone = document.getElementById('fileDropZone');

            dropZone.addEventListener('dragover', function(e) {
                e.preventDefault();
                dropZone.classList.add('dragover');
            });

            dropZone.addEventListener('dragleave', function(e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');
            });

            dropZone.addEventListener('drop', function(e) {
                e.preventDefault();
                dropZone.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileSelect({ files: files });
                }
            });
        }

        // Statistics and Overview Functions
        function loadBackupStatistics() {
            try {
                // Get backup statistics from localStorage or server
                const stats = getBackupStatistics();

                document.getElementById('totalBackups').textContent = stats.totalBackups;
                document.getElementById('lastBackupTime').textContent = stats.lastBackupTime;
                document.getElementById('totalSize').textContent = stats.totalSize;
                document.getElementById('systemHealth').textContent = stats.systemHealth;

                // Update export info
                updateExportInfo();

            } catch (error) {
                console.error('خطأ في تحميل الإحصائيات:', error);
                showNotification('خطأ في تحميل الإحصائيات', 'error');
            }
        }

        function getBackupStatistics() {
            // Calculate statistics from localStorage and server data
            let totalBackups = 0;
            let lastBackupTime = 'غير محدد';
            let totalSize = '0 KB';
            let systemHealth = 'ممتاز';

            // Check localStorage for backup history
            const backupHistory = JSON.parse(localStorage.getItem('backupHistory') || '[]');
            totalBackups = backupHistory.length;

            if (backupHistory.length > 0) {
                const lastBackup = backupHistory[backupHistory.length - 1];
                lastBackupTime = new Date(lastBackup.timestamp).toLocaleString('ar-SA');

                // Calculate total size
                let totalBytes = backupHistory.reduce((sum, backup) => sum + (backup.size || 0), 0);
                totalSize = formatFileSize(totalBytes);
            }

            // Check system health based on data integrity
            systemHealth = checkSystemHealth();

            return {
                totalBackups,
                lastBackupTime,
                totalSize,
                systemHealth
            };
        }

        function checkSystemHealth() {
            try {
                // Check if essential data exists
                const users = localStorage.getItem('systemUsers');
                const events = localStorage.getItem('securityEvents');
                const activities = localStorage.getItem('activityLogs');

                if (!users || !events || !activities) {
                    return 'يحتاج انتباه';
                }

                // Check data integrity
                JSON.parse(users);
                JSON.parse(events);
                JSON.parse(activities);

                return 'ممتاز';
            } catch (error) {
                return 'خطأ في البيانات';
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';

            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Quick Backup Function
        function createQuickBackup() {
            try {
                showNotification('جاري إنشاء النسخة الاحتياطية...', 'info');

                // Collect all data
                const backupData = {
                    timestamp: new Date().toISOString(),
                    version: '3.0',
                    type: 'manual',
                    systemInfo: {
                        userAgent: navigator.userAgent,
                        url: window.location.href,
                        language: navigator.language
                    },
                    data: {}
                };

                // Important data keys
                const importantKeys = [
                    'systemUsers',
                    'userSessions',
                    'currentSession',
                    'securityEvents',
                    'systemSettings',
                    'activityLogs',
                    'riskAssessments',
                    'incidentReports',
                    'complianceData',
                    'auditTrails'
                ];

                importantKeys.forEach(key => {
                    const value = localStorage.getItem(key);
                    if (value) {
                        try {
                            backupData.data[key] = JSON.parse(value);
                        } catch (e) {
                            backupData.data[key] = value;
                        }
                    }
                });

                // Add sync data if available
                if (window.networkSyncClient && window.networkSyncClient.localData) {
                    backupData.syncData = window.networkSyncClient.localData;
                }

                // Create filename
                const now = new Date();
                const dateStr = now.toISOString().split('T')[0];
                const timeStr = now.toTimeString().split(' ')[0].replace(/:/g, '-');
                const filename = `ISMS-Backup-${dateStr}-${timeStr}.json`;

                // Convert to JSON and download
                const jsonString = JSON.stringify(backupData, null, 2);
                const blob = new Blob([jsonString], { type: 'application/json' });
                const url = URL.createObjectURL(blob);

                const a = document.createElement('a');
                a.href = url;
                a.download = filename;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);

                // Save to backup history
                saveBackupToHistory({
                    filename: filename,
                    timestamp: backupData.timestamp,
                    size: jsonString.length,
                    type: 'manual',
                    dataTypes: Object.keys(backupData.data).length
                });

                // Log activity
                if (typeof logActivity === 'function') {
                    logActivity('backup_created', 'تم إنشاء نسخة احتياطية سريعة', {
                        filename: filename,
                        dataSize: jsonString.length,
                        keysCount: Object.keys(backupData.data).length
                    });
                }

                showNotification('تم إنشاء النسخة الاحتياطية بنجاح!', 'success');

                // Refresh statistics
                setTimeout(() => {
                    loadBackupStatistics();
                    loadBackupList();
                }, 1000);

            } catch (error) {
                console.error('خطأ في إنشاء النسخة الاحتياطية:', error);
                showNotification('فشل في إنشاء النسخة الاحتياطية: ' + error.message, 'error');
            }
        }

        function saveBackupToHistory(backupInfo) {
            try {
                const history = JSON.parse(localStorage.getItem('backupHistory') || '[]');
                history.push(backupInfo);

                // Keep only last 50 backups in history
                if (history.length > 50) {
                    history.splice(0, history.length - 50);
                }

                localStorage.setItem('backupHistory', JSON.stringify(history));
            } catch (error) {
                console.error('خطأ في حفظ تاريخ النسخ الاحتياطية:', error);
            }
        }

        // Export Functions
        function updateExportInfo() {
            const selectedData = [];
            const checkboxes = ['exportUsers', 'exportEvents', 'exportActivities', 'exportSettings'];

            checkboxes.forEach(id => {
                if (document.getElementById(id).checked) {
                    selectedData.push(id.replace('export', ''));
                }
            });

            document.getElementById('selectedDataCount').textContent = selectedData.length;

            // Estimate size
            let estimatedSize = 0;
            selectedData.forEach(dataType => {
                const key = getStorageKey(dataType);
                const data = localStorage.getItem(key);
                if (data) {
                    estimatedSize += data.length;
                }
            });

            document.getElementById('estimatedSize').textContent = formatFileSize(estimatedSize);
            document.getElementById('exportTime').textContent = estimatedSize > 100000 ? '~10 ثوان' : '~5 ثوان';
        }

        function getStorageKey(dataType) {
            const keyMap = {
                'Users': 'systemUsers',
                'Events': 'securityEvents',
                'Activities': 'activityLogs',
                'Settings': 'systemSettings'
            };
            return keyMap[dataType] || dataType.toLowerCase();
        }

        function exportData() {
            try {
                const format = document.querySelector('input[name="exportFormat"]:checked').value;
                const includeMetadata = document.getElementById('includeMetadata').checked;
                const compress = document.getElementById('compressFile').checked;

                showNotification('جاري تصدير البيانات...', 'info');

                // Collect selected data
                const exportData = {
                    timestamp: new Date().toISOString(),
                    version: '3.0',
                    exportFormat: format,
                    metadata: includeMetadata ? {
                        exportedBy: getCurrentUser(),
                        systemInfo: {
                            userAgent: navigator.userAgent,
                            url: window.location.href
                        }
                    } : null,
                    data: {}
                };

                // Get selected data types
                const checkboxes = ['exportUsers', 'exportEvents', 'exportActivities', 'exportSettings'];
                checkboxes.forEach(id => {
                    if (document.getElementById(id).checked) {
                        const dataType = id.replace('export', '');
                        const key = getStorageKey(dataType);
                        const data = localStorage.getItem(key);
                        if (data) {
                            try {
                                exportData.data[dataType] = JSON.parse(data);
                            } catch (e) {
                                exportData.data[dataType] = data;
                            }
                        }
                    }
                });

                // Export based on format
                switch (format) {
                    case 'json':
                        exportAsJSON(exportData);
                        break;
                    case 'excel':
                        exportAsExcel(exportData);
                        break;
                    case 'csv':
                        exportAsCSV(exportData);
                        break;
                }

                // Log activity
                if (typeof logActivity === 'function') {
                    logActivity('data_exported', `تم تصدير البيانات بتنسيق ${format}`, {
                        format: format,
                        dataTypes: Object.keys(exportData.data).length,
                        includeMetadata: includeMetadata
                    });
                }

                showNotification('تم تصدير البيانات بنجاح!', 'success');

            } catch (error) {
                console.error('خطأ في تصدير البيانات:', error);
                showNotification('فشل في تصدير البيانات: ' + error.message, 'error');
            }
        }

        function exportAsJSON(data) {
            const jsonString = JSON.stringify(data, null, 2);
            const blob = new Blob([jsonString], { type: 'application/json' });
            downloadFile(blob, `ISMS-Export-${new Date().toISOString().split('T')[0]}.json`);
        }

        function exportAsExcel(data) {
            const workbook = XLSX.utils.book_new();

            // Create sheets for each data type
            Object.keys(data.data).forEach(dataType => {
                const sheetData = data.data[dataType];
                let worksheet;

                if (Array.isArray(sheetData)) {
                    worksheet = XLSX.utils.json_to_sheet(sheetData);
                } else if (typeof sheetData === 'object') {
                    // Convert object to array of key-value pairs
                    const arrayData = Object.keys(sheetData).map(key => ({
                        Key: key,
                        Value: JSON.stringify(sheetData[key])
                    }));
                    worksheet = XLSX.utils.json_to_sheet(arrayData);
                } else {
                    worksheet = XLSX.utils.json_to_sheet([{Data: sheetData}]);
                }

                XLSX.utils.book_append_sheet(workbook, worksheet, dataType);
            });

            // Add metadata sheet if included
            if (data.metadata) {
                const metadataSheet = XLSX.utils.json_to_sheet([data.metadata]);
                XLSX.utils.book_append_sheet(workbook, metadataSheet, 'Metadata');
            }

            const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
            const blob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            downloadFile(blob, `ISMS-Export-${new Date().toISOString().split('T')[0]}.xlsx`);
        }

        function exportAsCSV(data) {
            // For CSV, we'll export each data type as a separate file in a zip
            // For simplicity, let's export the first data type as CSV
            const firstDataType = Object.keys(data.data)[0];
            const firstData = data.data[firstDataType];

            let csvContent = '';

            if (Array.isArray(firstData)) {
                if (firstData.length > 0) {
                    // Get headers
                    const headers = Object.keys(firstData[0]);
                    csvContent = headers.join(',') + '\n';

                    // Add data rows
                    firstData.forEach(row => {
                        const values = headers.map(header => {
                            const value = row[header];
                            return typeof value === 'string' ? `"${value.replace(/"/g, '""')}"` : value;
                        });
                        csvContent += values.join(',') + '\n';
                    });
                }
            } else {
                // Convert object to CSV
                csvContent = 'Key,Value\n';
                Object.keys(firstData).forEach(key => {
                    const value = typeof firstData[key] === 'string' ?
                        `"${firstData[key].replace(/"/g, '""')}"` :
                        JSON.stringify(firstData[key]);
                    csvContent += `"${key}",${value}\n`;
                });
            }

            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            downloadFile(blob, `ISMS-Export-${firstDataType}-${new Date().toISOString().split('T')[0]}.csv`);
        }

        function downloadFile(blob, filename) {
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
        }

        function getCurrentUser() {
            try {
                const currentSession = JSON.parse(localStorage.getItem('currentSession') || '{}');
                return currentSession.username || 'غير محدد';
            } catch (error) {
                return 'غير محدد';
            }
        }

        function scheduleExport() {
            showNotification('ميزة الجدولة قيد التطوير', 'info');
        }

        // Import Functions
        function handleFileSelect(input) {
            const file = input.files[0];
            if (!file) return;

            showNotification('جاري تحليل الملف...', 'info');

            // Show file info
            displayFileInfo(file);

            // Read and validate file
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    let data;
                    const fileExtension = file.name.split('.').pop().toLowerCase();

                    switch (fileExtension) {
                        case 'json':
                            data = JSON.parse(e.target.result);
                            break;
                        case 'xlsx':
                            // Handle Excel files
                            const workbook = XLSX.read(e.target.result, { type: 'binary' });
                            data = parseExcelWorkbook(workbook);
                            break;
                        case 'csv':
                            data = parseCSV(e.target.result);
                            break;
                        default:
                            throw new Error('نوع ملف غير مدعوم');
                    }

                    // Validate data structure
                    if (validateImportData(data)) {
                        currentImportData = data;
                        showImportOptions();
                        showNotification('تم تحليل الملف بنجاح', 'success');
                    } else {
                        throw new Error('بنية الملف غير صحيحة');
                    }

                } catch (error) {
                    console.error('خطأ في قراءة الملف:', error);
                    showNotification('خطأ في قراءة الملف: ' + error.message, 'error');
                    resetImportForm();
                }
            };

            if (file.name.endsWith('.xlsx')) {
                reader.readAsBinaryString(file);
            } else {
                reader.readAsText(file);
            }
        }

        function displayFileInfo(file) {
            const filePreview = document.getElementById('filePreview');
            filePreview.innerHTML = `
                <div class="file-info">
                    <h6><i class="fas fa-file"></i> معلومات الملف</h6>
                    <p><strong>الاسم:</strong> ${file.name}</p>
                    <p><strong>الحجم:</strong> ${formatFileSize(file.size)}</p>
                    <p><strong>النوع:</strong> ${file.type || 'غير محدد'}</p>
                    <p><strong>آخر تعديل:</strong> ${new Date(file.lastModified).toLocaleString('ar-SA')}</p>
                </div>
            `;
        }

        function parseExcelWorkbook(workbook) {
            const data = {};
            workbook.SheetNames.forEach(sheetName => {
                const worksheet = workbook.Sheets[sheetName];
                data[sheetName] = XLSX.utils.sheet_to_json(worksheet);
            });
            return { data: data, version: '3.0', timestamp: new Date().toISOString() };
        }

        function parseCSV(csvText) {
            const lines = csvText.split('\n');
            const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
            const data = [];

            for (let i = 1; i < lines.length; i++) {
                if (lines[i].trim()) {
                    const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
                    const row = {};
                    headers.forEach((header, index) => {
                        row[header] = values[index] || '';
                    });
                    data.push(row);
                }
            }

            return { data: { ImportedData: data }, version: '3.0', timestamp: new Date().toISOString() };
        }

        function validateImportData(data) {
            // Basic validation
            if (!data || typeof data !== 'object') return false;
            if (!data.data || typeof data.data !== 'object') return false;

            // Check if it's a valid backup format
            return true; // For now, accept any valid JSON structure
        }

        function showImportOptions() {
            document.getElementById('importOptions').style.display = 'block';

            // Show data preview
            const previewHtml = generateDataPreview(currentImportData);
            document.getElementById('filePreview').innerHTML = previewHtml;
        }

        function generateDataPreview(data) {
            let html = '<div class="data-preview"><h6><i class="fas fa-eye"></i> معاينة البيانات</h6>';

            if (data.data) {
                Object.keys(data.data).forEach(key => {
                    const value = data.data[key];
                    const count = Array.isArray(value) ? value.length : Object.keys(value).length;
                    html += `<p><strong>${key}:</strong> ${count} عنصر</p>`;
                });
            }

            if (data.timestamp) {
                html += `<p><strong>تاريخ الإنشاء:</strong> ${new Date(data.timestamp).toLocaleString('ar-SA')}</p>`;
            }

            html += '</div>';
            return html;
        }

        function confirmImport() {
            if (!currentImportData) {
                showNotification('لم يتم اختيار ملف للاستيراد', 'warning');
                return;
            }

            // Show confirmation modal
            const modal = new bootstrap.Modal(document.getElementById('importModal'));

            // Populate modal content
            const content = document.getElementById('importConfirmationContent');
            content.innerHTML = generateImportConfirmation();

            modal.show();
        }

        function generateImportConfirmation() {
            const mode = document.querySelector('input[name="importMode"]:checked').value;
            const createBackup = document.getElementById('createBackupBeforeImport').checked;

            let html = '<div class="import-confirmation">';
            html += '<h6><i class="fas fa-exclamation-triangle text-warning"></i> تأكيد الاستيراد</h6>';
            html += '<p>أنت على وشك استيراد البيانات التالية:</p>';

            if (currentImportData.data) {
                html += '<ul>';
                Object.keys(currentImportData.data).forEach(key => {
                    const value = currentImportData.data[key];
                    const count = Array.isArray(value) ? value.length : Object.keys(value).length;
                    html += `<li><strong>${key}:</strong> ${count} عنصر</li>`;
                });
                html += '</ul>';
            }

            html += `<p><strong>وضع الاستيراد:</strong> ${mode === 'merge' ? 'دمج مع البيانات الموجودة' : 'استبدال البيانات الموجودة'}</p>`;
            html += `<p><strong>نسخة احتياطية:</strong> ${createBackup ? 'سيتم إنشاؤها تلقائياً' : 'لن يتم إنشاؤها'}</p>`;

            html += '<div class="alert alert-warning mt-3">';
            html += '<strong>تحذير:</strong> هذه العملية لا يمكن التراجع عنها. تأكد من صحة البيانات قبل المتابعة.';
            html += '</div>';

            html += '</div>';
            return html;
        }

        function executeImport() {
            if (!currentImportData) return;

            try {
                const mode = document.querySelector('input[name="importMode"]:checked').value;
                const createBackup = document.getElementById('createBackupBeforeImport').checked;

                // Close modal
                bootstrap.Modal.getInstance(document.getElementById('importModal')).hide();

                // Show progress
                showImportProgress();

                // Create backup if requested
                if (createBackup) {
                    updateImportProgress(20, 'إنشاء نسخة احتياطية...');
                    createQuickBackup();
                }

                // Import data
                updateImportProgress(50, 'استيراد البيانات...');

                setTimeout(() => {
                    if (mode === 'replace') {
                        replaceData(currentImportData.data);
                    } else {
                        mergeData(currentImportData.data);
                    }

                    updateImportProgress(90, 'إنهاء العملية...');

                    setTimeout(() => {
                        updateImportProgress(100, 'تم الاستيراد بنجاح!');

                        // Log activity
                        if (typeof logActivity === 'function') {
                            logActivity('data_imported', 'تم استيراد البيانات', {
                                mode: mode,
                                dataTypes: Object.keys(currentImportData.data).length,
                                backupCreated: createBackup
                            });
                        }

                        showNotification('تم استيراد البيانات بنجاح!', 'success');

                        setTimeout(() => {
                            hideImportProgress();
                            resetImportForm();
                            loadBackupStatistics();

                            // Ask if user wants to reload page
                            if (confirm('تم استيراد البيانات بنجاح. هل تريد إعادة تحميل الصفحة لرؤية التغييرات؟')) {
                                window.location.reload();
                            }
                        }, 2000);

                    }, 1000);
                }, 1500);

            } catch (error) {
                console.error('خطأ في الاستيراد:', error);
                showNotification('فشل في استيراد البيانات: ' + error.message, 'error');
                hideImportProgress();
            }
        }

        function showImportProgress() {
            document.getElementById('importProgress').style.display = 'block';
        }

        function updateImportProgress(percentage, status) {
            document.getElementById('importProgressBar').style.width = percentage + '%';
            document.getElementById('importStatus').textContent = status;
        }

        function hideImportProgress() {
            document.getElementById('importProgress').style.display = 'none';
        }

        function replaceData(importData) {
            Object.keys(importData).forEach(key => {
                const storageKey = getStorageKeyForImport(key);
                localStorage.setItem(storageKey, JSON.stringify(importData[key]));
            });
        }

        function mergeData(importData) {
            Object.keys(importData).forEach(key => {
                const storageKey = getStorageKeyForImport(key);
                const existingData = localStorage.getItem(storageKey);

                if (existingData) {
                    try {
                        const existing = JSON.parse(existingData);
                        const imported = importData[key];

                        let merged;
                        if (Array.isArray(existing) && Array.isArray(imported)) {
                            // Merge arrays, avoiding duplicates if possible
                            merged = [...existing, ...imported];
                        } else if (typeof existing === 'object' && typeof imported === 'object') {
                            // Merge objects
                            merged = { ...existing, ...imported };
                        } else {
                            // Replace if types don't match
                            merged = imported;
                        }

                        localStorage.setItem(storageKey, JSON.stringify(merged));
                    } catch (e) {
                        // If parsing fails, replace
                        localStorage.setItem(storageKey, JSON.stringify(importData[key]));
                    }
                } else {
                    localStorage.setItem(storageKey, JSON.stringify(importData[key]));
                }
            });
        }

        function getStorageKeyForImport(key) {
            // Map import keys to localStorage keys
            const keyMap = {
                'Users': 'systemUsers',
                'Events': 'securityEvents',
                'Activities': 'activityLogs',
                'Settings': 'systemSettings',
                'systemUsers': 'systemUsers',
                'securityEvents': 'securityEvents',
                'activityLogs': 'activityLogs',
                'systemSettings': 'systemSettings'
            };
            return keyMap[key] || key;
        }

        function resetImportForm() {
            currentImportData = null;
            document.getElementById('importFile').value = '';
            document.getElementById('importOptions').style.display = 'none';
            document.getElementById('filePreview').innerHTML = '<p class="text-muted text-center">لم يتم اختيار ملف</p>';
        }

        function cancelImport() {
            resetImportForm();
            showNotification('تم إلغاء عملية الاستيراد', 'info');
        }

        function showImportModal() {
            // Switch to import tab
            const importTab = document.getElementById('import-tab');
            importTab.click();
        }

        // Backup Management Functions
        function loadBackupList() {
            try {
                const history = JSON.parse(localStorage.getItem('backupHistory') || '[]');
                backupList = history;
                displayBackupList(history);
            } catch (error) {
                console.error('خطأ في تحميل قائمة النسخ الاحتياطية:', error);
                showNotification('خطأ في تحميل قائمة النسخ الاحتياطية', 'error');
            }
        }

        function displayBackupList(backups) {
            const tbody = document.getElementById('backupTableBody');

            if (backups.length === 0) {
                tbody.innerHTML = '<tr><td colspan="6" class="text-center text-muted">لا توجد نسخ احتياطية</td></tr>';
                return;
            }

            tbody.innerHTML = backups.map(backup => `
                <tr>
                    <td>
                        <i class="fas fa-file-archive text-primary"></i>
                        ${backup.filename}
                    </td>
                    <td>${new Date(backup.timestamp).toLocaleString('ar-SA')}</td>
                    <td>${formatFileSize(backup.size)}</td>
                    <td>
                        <span class="badge bg-${getTypeColor(backup.type)}">${getTypeLabel(backup.type)}</span>
                    </td>
                    <td>
                        <span class="badge bg-success">مكتمل</span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="downloadBackup('${backup.filename}')" title="تحميل">
                            <i class="fas fa-download"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="viewBackupDetails('${backup.filename}')" title="عرض التفاصيل">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteBackup('${backup.filename}')" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function getTypeColor(type) {
            const colors = {
                'manual': 'primary',
                'automatic': 'success',
                'scheduled': 'info'
            };
            return colors[type] || 'secondary';
        }

        function getTypeLabel(type) {
            const labels = {
                'manual': 'يدوي',
                'automatic': 'تلقائي',
                'scheduled': 'مجدول'
            };
            return labels[type] || 'غير محدد';
        }

        function filterBackups() {
            const searchTerm = document.getElementById('searchBackups').value.toLowerCase();
            const filterType = document.getElementById('filterType').value;

            let filteredBackups = backupList;

            if (searchTerm) {
                filteredBackups = filteredBackups.filter(backup =>
                    backup.filename.toLowerCase().includes(searchTerm)
                );
            }

            if (filterType) {
                filteredBackups = filteredBackups.filter(backup =>
                    backup.type === filterType
                );
            }

            displayBackupList(filteredBackups);
        }

        function refreshBackupList() {
            showNotification('جاري تحديث القائمة...', 'info');
            loadBackupList();
            loadBackupStatistics();
            showNotification('تم تحديث القائمة', 'success');
        }

        function downloadBackup(filename) {
            showNotification('ميزة التحميل من الخادم قيد التطوير', 'info');
        }

        function viewBackupDetails(filename) {
            const backup = backupList.find(b => b.filename === filename);
            if (backup) {
                alert(`تفاصيل النسخة الاحتياطية:\n\nالاسم: ${backup.filename}\nالتاريخ: ${new Date(backup.timestamp).toLocaleString('ar-SA')}\nالحجم: ${formatFileSize(backup.size)}\nالنوع: ${getTypeLabel(backup.type)}\nأنواع البيانات: ${backup.dataTypes || 'غير محدد'}`);
            }
        }

        function deleteBackup(filename) {
            if (confirm(`هل أنت متأكد من حذف النسخة الاحتياطية "${filename}"؟\n\nهذا الإجراء لا يمكن التراجع عنه.`)) {
                try {
                    const history = JSON.parse(localStorage.getItem('backupHistory') || '[]');
                    const updatedHistory = history.filter(backup => backup.filename !== filename);
                    localStorage.setItem('backupHistory', JSON.stringify(updatedHistory));

                    loadBackupList();
                    loadBackupStatistics();

                    showNotification('تم حذف النسخة الاحتياطية', 'success');

                    if (typeof logActivity === 'function') {
                        logActivity('backup_deleted', 'تم حذف نسخة احتياطية', { filename: filename });
                    }
                } catch (error) {
                    console.error('خطأ في حذف النسخة الاحتياطية:', error);
                    showNotification('فشل في حذف النسخة الاحتياطية', 'error');
                }
            }
        }

        // Utility Functions
        function showNotification(message, type = 'info') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notification => notification.remove());

            // Create new notification
            const notification = document.createElement('div');
            notification.className = `alert alert-${type === 'error' ? 'danger' : type} notification alert-dismissible fade show`;
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Navigation Functions
        function goToMainSystem() {
            window.location.href = 'index.html';
        }

        function goToLogin() {
            window.location.href = 'login.html';
        }
    </script>
</body>
</html>
