@echo off
chcp 65001 > nul
title تثبيت ISMS-Distribution-v1.0.0

echo.
echo ==========================================
echo    نظام إدارة أمن المعلومات
echo    حزمة التوزيع الشاملة
echo    الإصدار: 1.0.0
echo ==========================================
echo.

echo 📦 مرحباً بك في مثبت نظام إدارة أمن المعلومات
echo.
echo 🔍 اختر طريقة التثبيت:
echo.
echo [1] تثبيت سريع (مستحسن)
echo [2] تثبيت مخصص
echo [3] النسخة المحمولة
echo [4] التحقق من النظام
echo [5] عرض معلومات الحزمة
echo [0] خروج
echo.
set /p choice="اختر رقم (0-5): "

if "%choice%"=="1" goto :quick_install
if "%choice%"=="2" goto :custom_install
if "%choice%"=="3" goto :portable
if "%choice%"=="4" goto :verify
if "%choice%"=="5" goto :info
if "%choice%"=="0" goto :exit
goto :invalid

:quick_install
echo.
echo 🚀 بدء التثبيت السريع...
cd Core
call ..\Tools\quick-setup.bat
goto :end

:custom_install
echo.
echo 🔧 التثبيت المخصص...
cd Core
call ..\Tools\install-simple.bat
goto :end

:portable
echo.
echo 📁 النسخة المحمولة...
cd Tools
if exist "ISMS-Portable-v1.0.0.zip" (
    echo ✅ فك ضغط النسخة المحمولة...
    powershell -Command "Expand-Archive -Path 'ISMS-Portable-v1.0.0.zip' -DestinationPath '../ISMS-Portable' -Force"
    echo 🎉 تم فك الضغط بنجاح!
    echo 📍 المسار: ISMS-Portable
) else (
    echo ❌ ملف النسخة المحمولة غير موجود
)
goto :end

:verify
echo.
echo 🔍 التحقق من النظام...
cd Core
python ..\Toolserify-installation.py
goto :end

:info
echo.
echo 📋 معلومات الحزمة...
type package-info.json
goto :end

:invalid
echo ❌ اختيار غير صحيح
goto :end

:exit
echo 👋 شكراً لاستخدام نظام إدارة أمن المعلومات
exit /b 0

:end
echo.
echo 📋 معلومات مهمة:
echo    👤 اسم المستخدم: admin
echo    🔒 كلمة المرور: admin123
echo    🌐 الرابط: http://localhost:8000
echo.
pause
