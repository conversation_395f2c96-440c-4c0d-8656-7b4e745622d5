# نظام إدارة أمن المعلومات

نظام احترافي لإدارة وتتبع الأحداث الأمنية مع واجهة مستخدم حديثة ومتجاوبة.

## المميزات

### 🎨 التصميم
- **ألوان احترافية**: تصميم بألوان زرقاء ورمادية أنيقة
- **وضع ليلي/نهاري**: إمكانية التبديل بين الوضعين حسب التفضيل
- **تصميم متجاوب**: يعمل بشكل مثالي على جميع الأجهزة (الكمبيوتر، التابلت، الهاتف)
- **واجهة عربية**: دعم كامل للغة العربية مع اتجاه RTL

### 📝 إدارة الأحداث
- **إدخال يدوي**: نموذج سهل لإدخال الأحداث الأمنية
- **تصنيف الخطورة**: أربعة مستويات (منخفض، متوسط، عالي، حرج)
- **أنواع الأحداث**: تصنيفات متنوعة للأحداث الأمنية
- **تتبع الحالة**: متابعة حالة كل حدث (مفتوح، قيد التحقيق، محلول، مغلق)

### 🔍 البحث والتصفية
- **البحث النصي**: البحث في عناوين ووصف الأحداث
- **تصفية حسب الخطورة**: عرض الأحداث حسب مستوى الخطورة
- **عرض تفصيلي**: إمكانية عرض تفاصيل كاملة لكل حدث

### ⚡ الوظائف التفاعلية
- **تعديل الأحداث**: إمكانية تعديل بيانات الأحداث الموجودة
- **حذف الأحداث**: حذف الأحداث مع تأكيد الأمان
- **حفظ محلي**: حفظ البيانات في متصفح المستخدم
- **إشعارات**: رسائل تأكيد للعمليات المختلفة

## بنية المشروع

```
├── index.html          # الصفحة الرئيسية
├── styles.css          # ملف الأنماط والتصميم
├── script.js           # ملف JavaScript للوظائف
└── README.md           # ملف التوثيق
```

## كيفية الاستخدام

### 1. تشغيل النظام
- افتح ملف `index.html` في أي متصفح حديث
- لا يحتاج إلى خادم ويب أو قاعدة بيانات

### 2. إدخال حدث جديد
1. املأ نموذج "إدخال حدث أمني جديد"
2. اختر مستوى الخطورة ونوع الحدث
3. أدخل الوصف والتفاصيل المطلوبة
4. اضغط "حفظ الحدث"

### 3. إدارة الأحداث
- **عرض**: اضغط على أيقونة العين لعرض تفاصيل الحدث
- **تعديل**: اضغط على أيقونة القلم لتعديل الحدث
- **حذف**: اضغط على أيقونة سلة المهملات لحذف الحدث

### 4. البحث والتصفية
- استخدم مربع البحث للبحث في النصوص
- استخدم قائمة التصفية لعرض أحداث بمستوى خطورة معين

### 5. تغيير الوضع
- اضغط على أيقونة القمر/الشمس في الرأس لتغيير الوضع

## المتطلبات التقنية

### المتصفحات المدعومة
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### التقنيات المستخدمة
- **HTML5**: هيكل الصفحة
- **CSS3**: التصميم والأنماط
- **JavaScript ES6+**: الوظائف التفاعلية
- **Local Storage**: حفظ البيانات محلياً
- **Font Awesome**: الأيقونات
- **Google Fonts**: خط Cairo العربي

## الميزات الأمنية

### حماية البيانات
- حفظ البيانات محلياً في المتصفح فقط
- لا يتم إرسال أي بيانات لخوادم خارجية
- تشفير البيانات في Local Storage

### التحقق من صحة البيانات
- التحقق من صحة جميع الحقول المطلوبة
- منع إدخال البيانات الفارغة أو غير الصحيحة
- تنظيف البيانات المدخلة

## التخصيص

### إضافة شعار مخصص

يدعم النظام إضافة شعار مخصص:

1. **إضافة شعار جديد:**
   - ضع ملف الشعار في مجلد النظام باسم `logo.jpg`
   - الأحجام المدعومة: JPG, PNG, SVG
   - الحجم المفضل: 200x200 بكسل

2. **إنشاء شعار تجريبي:**
   - افتح `create-logo.html` في المتصفح
   - اختر التصميم المناسب
   - اضغط "تحميل الشعار" لحفظ الملف

3. **الشعار التلقائي:**
   - إذا لم يوجد ملف `logo.jpg`، سيظهر رمز الدرع الافتراضي
   - يعمل النظام بشكل طبيعي مع أو بدون شعار مخصص

### تغيير الألوان
يمكن تخصيص الألوان من خلال تعديل متغيرات CSS في بداية ملف `styles.css`:

```css
:root {
    --primary-blue: #2563eb;
    --secondary-blue: #3b82f6;
    --primary-gray: #6b7280;
    /* ... باقي المتغيرات */
}
```

### إضافة أنواع أحداث جديدة
يمكن إضافة أنواع أحداث جديدة من خلال تعديل:
1. قائمة الخيارات في `index.html`
2. دالة `getTypeLabel()` في `script.js`

## الدعم والمساعدة

### المشاكل الشائعة
- **البيانات لا تحفظ**: تأكد من تفعيل Local Storage في المتصفح
- **التصميم لا يظهر بشكل صحيح**: تأكد من اتصال الإنترنت لتحميل الخطوط والأيقونات

### التطوير المستقبلي
- [ ] إضافة قاعدة بيانات خارجية
- [ ] تصدير البيانات إلى Excel/PDF
- [ ] إضافة نظام المستخدمين والصلاحيات
- [ ] إضافة التقارير والإحصائيات
- [ ] دمج مع أنظمة SIEM خارجية

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**تم تطوير هذا النظام وفقاً لأفضل الممارسات في أمن المعلومات وتجربة المستخدم**
