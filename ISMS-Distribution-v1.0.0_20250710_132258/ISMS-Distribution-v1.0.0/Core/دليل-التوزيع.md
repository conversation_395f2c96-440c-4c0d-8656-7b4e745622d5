# دليل توزيع نظام إدارة أمن المعلومات

## نظرة عامة
هذا الدليل يوضح كيفية إنشاء حزم التثبيت المختلفة لنظام إدارة أمن المعلومات وتوزيعها على الأجهزة الأخرى.

## 🚀 التثبيت السريع (للمستخدمين)

### الطريقة الأسهل:
1. شغل `quick-setup.bat`
2. اختر "تثبيت سريع"
3. انتظر حتى انتهاء التثبيت
4. شغل النظام من اختصار سطح المكتب

## 📦 طرق إنشاء حزم التوزيع

### 1. النسخة المحمولة (Portable)
```bash
# إنشاء نسخة محمولة
python create-installer.py portable
```
**المخرجات:**
- `ISMS-Portable-v1.0.0.zip` - ملف مضغوط يحتوي على جميع الملفات
- لا يحتاج تثبيت، فقط فك الضغط والتشغيل

### 2. الملف التنفيذي (Executable)
```bash
# بناء ملف تنفيذي
python create-installer.py exe
```
**المخرجات:**
- `dist/ISMS-System.exe` - ملف تنفيذي واحد
- يعمل بدون تثبيت Python

### 3. مثبت MSI (Windows Installer)
```bash
# إنشاء ملف WiX للمثبت
python create-installer.py msi
```
**المخرجات:**
- `isms.wxs` - ملف WiX
- **يتطلب:** WiX Toolset لإنشاء ملف MSI

### 4. مثبت NSIS
```bash
# إنشاء ملف NSIS
python create-installer.py nsis
```
**المخرجات:**
- `isms-nsis.nsi` - ملف NSIS
- **يتطلب:** NSIS لإنشاء المثبت

### 5. مثبت Batch بسيط
```bash
# إنشاء مثبت بسيط
python create-installer.py batch
```
**المخرجات:**
- `install-simple.bat` - مثبت بسيط
- `uninstall-simple.bat` - ملف إلغاء التثبيت

### 6. إنشاء جميع الأنواع
```bash
# إنشاء جميع أنواع المثبتات
python create-installer.py all
```

## 🛠️ أدوات إضافية مطلوبة

### لإنشاء مثبت MSI:
1. حمل وثبت [WiX Toolset](https://wixtoolset.org/)
2. شغل الأمر:
```bash
candle isms.wxs
light isms.wixobj
```

### لإنشاء مثبت NSIS:
1. حمل وثبت [NSIS](https://nsis.sourceforge.io/)
2. شغل الأمر:
```bash
makensis isms-nsis.nsi
```

## 📋 قائمة الملفات المطلوبة للتوزيع

### الملفات الأساسية:
- `index.html` - الصفحة الرئيسية
- `login.html` - صفحة تسجيل الدخول
- `styles.css` - ملف التنسيق الرئيسي
- `login-styles.css` - ملف تنسيق تسجيل الدخول
- `script.js` - الكود الرئيسي
- `login-script.js` - كود تسجيل الدخول
- `auth.js` - نظام المصادقة
- `demo-data.js` - البيانات التجريبية

### ملفات Python:
- `start-server.py` - خادم الويب
- `windows-service.py` - خدمة ويندوز
- `windows-app.py` - تطبيق ويندوز

### الملفات الإضافية:
- `cyber-security.ico` - أيقونة النظام
- `logo.jpg` - شعار النظام
- `manifest.json` - ملف التطبيق

## 🌐 طرق التوزيع

### 1. التوزيع المحلي
- نسخ الملفات على USB
- مشاركة عبر الشبكة المحلية
- إرسال عبر البريد الإلكتروني

### 2. التوزيع عبر الإنترنت
- رفع على GitHub Releases
- استخدام خدمات التخزين السحابي
- إنشاء موقع تحميل

### 3. التوزيع المؤسسي
- استخدام Group Policy
- نشر عبر SCCM
- التثبيت التلقائي عبر الشبكة

## 🔧 إعداد التثبيت التلقائي

### إنشاء ملف تكوين:
```json
{
  "auto_install": true,
  "install_path": "C:\\Program Files\\ISMS",
  "create_desktop_shortcut": true,
  "install_service": false,
  "start_after_install": true
}
```

### تشغيل التثبيت الصامت:
```bash
install-simple.bat /S /CONFIG=config.json
```

## 📊 مراقبة التثبيت

### سجلات التثبيت:
- Windows Event Log
- ملفات السجل في مجلد التثبيت
- تقارير الأخطاء

### التحقق من التثبيت:
```bash
# التحقق من حالة الخدمة
python windows-service.py status

# اختبار الخادم
curl http://localhost:8000
```

## 🔒 اعتبارات الأمان

### أثناء التوزيع:
- تشفير الملفات الحساسة
- التوقيع الرقمي للمثبتات
- فحص الفيروسات

### بعد التثبيت:
- تغيير كلمات المرور الافتراضية
- تكوين الجدار الناري
- تحديث النظام بانتظام

## 🆘 استكشاف الأخطاء

### مشاكل شائعة:
1. **Python غير مثبت:** حمل من python.org
2. **خطأ في الصلاحيات:** شغل كمدير
3. **منفذ مشغول:** غير المنفذ في الإعدادات
4. **ملفات مفقودة:** تأكد من اكتمال النسخ

### الحصول على المساعدة:
- راجع ملف `دليل-التثبيت.md`
- تحقق من سجلات الأخطاء
- تواصل مع فريق الدعم الفني

## 📈 تحديث النظام

### تحديث يدوي:
1. أوقف النظام
2. انسخ الملفات الجديدة
3. شغل النظام مرة أخرى

### تحديث تلقائي:
- استخدم نظام إدارة التحديثات
- تحقق من الإصدارات الجديدة دورياً
- اختبر التحديثات قبل النشر

---

## 📞 الدعم الفني
للحصول على الدعم الفني أو الإبلاغ عن مشاكل، يرجى التواصل مع فريق أمن المعلومات.
