#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف Setup لنظام إدارة أمن المعلومات
Setup file for Information Security Management System
"""

from setuptools import setup, find_packages
import os
import sys

# قراءة ملف README
def read_readme():
    try:
        with open('README.md', 'r', encoding='utf-8') as f:
            return f.read()
    except:
        return "نظام إدارة أمن المعلومات - Information Security Management System"

# قراءة المتطلبات
def read_requirements():
    requirements = []
    try:
        with open('requirements.txt', 'r', encoding='utf-8') as f:
            requirements = [line.strip() for line in f if line.strip() and not line.startswith('#')]
    except:
        # المتطلبات الأساسية
        requirements = [
            'pywin32>=227',
            'requests>=2.25.0',
        ]
    return requirements

# إعداد البيانات
setup(
    name="isms-system",
    version="1.0.0",
    author="Information Security Team",
    author_email="<EMAIL>",
    description="نظام إدارة أمن المعلومات",
    long_description=read_readme(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-repo/isms",
    packages=find_packages(),
    
    # تضمين الملفات الإضافية
    include_package_data=True,
    package_data={
        '': [
            '*.html',
            '*.css', 
            '*.js',
            '*.ico',
            '*.jpg',
            '*.png',
            '*.json',
            '*.md',
            '*.txt'
        ],
    },
    
    # المتطلبات
    install_requires=read_requirements(),
    
    # متطلبات إضافية
    extras_require={
        'dev': [
            'pyinstaller>=4.0',
            'pytest>=6.0',
            'black>=21.0',
        ],
        'service': [
            'pywin32>=227',
        ]
    },
    
    # نقاط الدخول
    entry_points={
        'console_scripts': [
            'isms-server=start-server:main',
            'isms-service=windows-service:main',
            'isms-app=windows-app:main',
        ],
    },
    
    # التصنيفات
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: System Administrators",
        "Topic :: Security",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.7",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Operating System :: Microsoft :: Windows",
    ],
    
    # متطلبات Python
    python_requires='>=3.7',
    
    # كلمات مفتاحية
    keywords="security information management system isms أمن معلومات",
    
    # معلومات المشروع
    project_urls={
        'Bug Reports': 'https://github.com/your-repo/isms/issues',
        'Source': 'https://github.com/your-repo/isms',
        'Documentation': 'https://github.com/your-repo/isms/wiki',
    },
)

# إنشاء ملف requirements.txt إذا لم يكن موجوداً
if not os.path.exists('requirements.txt'):
    requirements_content = """# متطلبات نظام إدارة أمن المعلومات
# ISMS System Requirements

# Windows Service Support
pywin32>=227

# HTTP Requests
requests>=2.25.0

# Development Tools (Optional)
# pyinstaller>=4.0
# pytest>=6.0
# black>=21.0
"""
    
    with open('requirements.txt', 'w', encoding='utf-8') as f:
        f.write(requirements_content)
    
    print("✅ تم إنشاء ملف requirements.txt")

# إنشاء ملف MANIFEST.in
manifest_content = """# تضمين جميع الملفات المطلوبة
include *.md
include *.txt
include *.html
include *.css
include *.js
include *.ico
include *.jpg
include *.png
include *.json

# تضمين ملفات Python
include *.py

# استبعاد ملفات التطوير
exclude build-exe.py
exclude create-installer.py
prune build/
prune dist/
prune __pycache__/
global-exclude *.pyc
global-exclude *.pyo
global-exclude *~
"""

if not os.path.exists('MANIFEST.in'):
    with open('MANIFEST.in', 'w', encoding='utf-8') as f:
        f.write(manifest_content)
    print("✅ تم إنشاء ملف MANIFEST.in")

print("\n🎉 تم إعداد ملف Setup بنجاح!")
print("\n📋 لتثبيت النظام:")
print("   pip install .")
print("\n📋 لتثبيت مع أدوات التطوير:")
print("   pip install .[dev]")
print("\n📋 لإنشاء حزمة للتوزيع:")
print("   python setup.py sdist bdist_wheel")
