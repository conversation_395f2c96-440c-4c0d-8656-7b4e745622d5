<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام إدارة أمن المعلومات</title>
    <!-- النظام يستخدم التقويم الإفرنجي (الميلادي) لجميع التواريخ -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="أمن المعلومات">
    <link rel="apple-touch-icon" href="logo.jpg">
    <link rel="stylesheet" href="styles.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- SheetJS library for Excel import/export -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script>
        // Register Service Worker
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/sw.js')
                    .then(registration => {
                        console.log('SW registered: ', registration);
                    })
                    .catch(registrationError => {
                        console.log('SW registration failed: ', registrationError);
                    });
            });
        }
    </script>
</head>
<body>
    <div class="app-container">
        <!-- Main Title Section -->
        <section class="main-title-section">
            <div class="title-content">
                <div class="logo interactive-glow">
                    <img src="logo.jpg" alt="شعار النظام" class="logo-image">
                    <i class="fas fa-shield-alt" style="display: none;"></i>
                    <h1>نظام إدارة أمن المعلومات</h1>
                </div>
                <div class="header-controls">
                    <button class="theme-toggle interactive-glow" id="themeToggle">
                        <i class="fas fa-moon"></i>
                    </button>
                    <div class="user-menu interactive-glow" id="userMenu">
                        <i class="fas fa-user-shield"></i>
                        <span id="currentUserName">المدير</span>
                        <div class="user-dropdown">
                            <button class="dropdown-item" id="userProfileBtn">
                                <i class="fas fa-user-edit"></i>
                                الملف الشخصي
                            </button>
                            <button class="dropdown-item" id="logoutBtn">
                                <i class="fas fa-sign-out-alt"></i>
                                <span class="sr-only">تسجيل الخروج</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Navigation Header -->
        <header class="header">
            <div class="header-content">
                <nav class="main-nav">
                    <button class="nav-btn active bounce-in" data-section="events">
                        <i class="fas fa-clipboard-list"></i>
                        <span>الأحداث</span>
                    </button>
                    <button class="nav-btn fade-in-delay-1" data-section="post-incident-review">
                        <i class="fas fa-search-plus"></i>
                        <span>مراجعة ما بعد الحادث</span>
                    </button>
                    <button class="nav-btn fade-in-delay-2" data-section="risk-analysis">
                        <i class="fas fa-shield-alt"></i>
                        <span>تحليل المخاطر</span>
                    </button>
                    <button class="nav-btn fade-in-delay-3" data-section="analytics">
                        <i class="fas fa-chart-line"></i>
                        <span>الإحصائيات</span>
                    </button>
                    <button class="nav-btn fade-in-delay-3" data-section="event-types">
                        <i class="fas fa-cogs"></i>
                        <span>إدارة أنواع الأحداث</span>
                    </button>
                    <button class="nav-btn admin-only fade-in-delay-2" data-section="user-management" style="display: none;">
                        <i class="fas fa-users-cog"></i>
                        <span>إدارة المستخدمين</span>
                    </button>
                </nav>
            </div>
        </header>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Events Section -->
            <div class="content-section active" id="events-section">
                <!-- Event Input Section -->
                <section class="event-input-section">
                    <div class="section-header">
                        <h2><i class="fas fa-plus-circle"></i> إدخال حدث أمني جديد</h2>
                    </div>
                <form class="event-form" id="eventForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="eventSerial">رقم التسلسل (تلقائي)</label>
                            <input type="text" id="eventSerial" name="eventSerial" readonly placeholder="سيتم إنشاؤه تلقائياً">
                        </div>
                        <div class="form-group">
                            <label for="eventTitle">عنوان الحدث</label>
                            <input type="text" id="eventTitle" name="eventTitle" required>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="eventSeverity">مستوى الخطورة</label>
                            <select id="eventSeverity" name="eventSeverity" required>
                                <option value="">اختر مستوى الخطورة</option>
                                <option value="low">منخفض</option>
                                <option value="medium">متوسط</option>
                                <option value="high">عالي</option>
                                <option value="critical">حرج</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="responsiblePerson">الشخص المسؤول عن الحدث</label>
                            <input type="text" id="responsiblePerson" name="responsiblePerson" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="eventType">نوع الحدث</label>
                            <select id="eventType" name="eventType" required>
                                <option value="">اختر نوع الحدث</option>
                                <option value="intrusion">محاولة اختراق</option>
                                <option value="malware">برمجيات خبيثة</option>
                                <option value="phishing">تصيد إلكتروني</option>
                                <option value="data-breach">تسريب بيانات</option>
                                <option value="unauthorized-access">وصول غير مصرح</option>
                                <option value="system-failure">فشل النظام</option>
                                <option value="other">أخرى</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="eventDate">تاريخ ووقت الحدث</label>
                            <input type="datetime-local" id="eventDate" name="eventDate" required>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="eventDescription">وصف الحدث مختصر</label>
                        <textarea id="eventDescription" name="eventDescription" rows="3" required placeholder="اكتب وصفاً مختصراً للحدث الأمني..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="eventDetailedDescription">وصف الحدث بالتفصيل</label>
                        <textarea id="eventDetailedDescription" name="eventDetailedDescription" rows="6" placeholder="اكتب وصفاً مفصلاً للحدث الأمني، الخطوات المتخذة، والتوصيات..."></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="affectedSystems">الأنظمة المتأثرة</label>
                            <input type="text" id="affectedSystems" name="affectedSystems" placeholder="مثال: خادم الويب، قاعدة البيانات">
                        </div>
                        <div class="form-group">
                            <label for="eventLocation">موقع الحدث</label>
                            <input type="text" id="eventLocation" name="eventLocation" placeholder="مثال: مركز البيانات، المكتب الرئيسي">
                        </div>
                    </div>

                    <div class="form-group">
                        <label for="eventNotes">ملاحظات إضافية</label>
                        <textarea id="eventNotes" name="eventNotes" rows="3" placeholder="أي ملاحظات أو معلومات إضافية..."></textarea>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="directCosts">التكاليف المباشرة</label>
                            <input type="text" id="directCosts" name="directCosts" placeholder="مثال: تكلفة الإصلاح، استبدال الأجهزة">
                        </div>
                        <div class="form-group">
                            <label for="indirectCosts">التكاليف الغير مباشرة</label>
                            <input type="text" id="indirectCosts" name="indirectCosts" placeholder="مثال: فقدان الإنتاجية، تأثير السمعة">
                        </div>
                    </div>



                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary bounce-in">
                            <i class="fas fa-save"></i> حفظ الحدث
                        </button>
                        <button type="reset" class="btn btn-secondary fade-in-delay-1">
                            <i class="fas fa-undo-alt"></i> إعادة تعيين
                        </button>
                    </div>
                </form>
            </section>

            <!-- Events Log Section -->
            <section class="events-log-section">
                <div class="section-header">
                    <h2><i class="fas fa-list"></i> سجل الأحداث الأمنية</h2>
                    <div class="log-controls">
                        <input type="text" id="searchEvents" placeholder="البحث في الأحداث..." class="search-input">
                        <select id="filterSeverity" class="filter-select">
                            <option value="">جميع مستويات الخطورة</option>
                            <option value="low">منخفض</option>
                            <option value="medium">متوسط</option>
                            <option value="high">عالي</option>
                            <option value="critical">حرج</option>
                        </select>
                        <div class="excel-controls">
                            <button class="btn btn-info bounce-in" id="exportExcelBtn">
                                <i class="fas fa-file-excel"></i>
                                تصدير Excel
                            </button>
                            <button class="btn btn-secondary fade-in-delay-1" id="importExcelBtn">
                                <i class="fas fa-cloud-upload-alt"></i>
                                استيراد Excel
                            </button>
                            <input type="file" id="excelFileInput" accept=".xlsx,.xls" style="display: none;">
                        </div>
                    </div>
                </div>
                <div class="events-table-container">
                    <table class="events-table" id="eventsTable">
                        <thead>
                            <tr>
                                <th>رقم التسلسل</th>
                                <th>العنوان</th>
                                <th>النوع</th>
                                <th>الخطورة</th>
                                <th>المسؤول</th>
                                <th>التكاليف</th>
                                <th>التاريخ</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="eventsTableBody">
                            <!-- Events will be populated here -->
                        </tbody>
                    </table>
                </div>
            </section>
            </div>

            <!-- Analytics Section -->
            <div class="content-section" id="analytics-section">
                <!-- Statistics Overview -->
                <section class="stats-overview">
                    <div class="section-header">
                        <h2><i class="fas fa-chart-bar"></i> نظرة عامة على الإحصائيات</h2>
                    </div>
                    <div class="stats-grid">
                        <div class="stat-card fade-in interactive-glow">
                            <div class="stat-icon">
                                <i class="fas fa-database"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="totalEvents">0</h3>
                                <p>إجمالي الأحداث</p>
                            </div>
                        </div>
                        <div class="stat-card critical fade-in-delay-1 interactive-glow pulse">
                            <div class="stat-icon">
                                <i class="fas fa-fire-alt"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="criticalEvents">0</h3>
                                <p>أحداث حرجة</p>
                            </div>
                        </div>
                        <div class="stat-card high fade-in-delay-2 interactive-glow">
                            <div class="stat-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="highEvents">0</h3>
                                <p>أحداث عالية الخطورة</p>
                            </div>
                        </div>
                        <div class="stat-card open fade-in-delay-3 interactive-glow">
                            <div class="stat-icon">
                                <i class="fas fa-folder-open"></i>
                            </div>
                            <div class="stat-content">
                                <h3 id="openEvents">0</h3>
                                <p>أحداث مفتوحة</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Charts Section -->
                <section class="charts-section fade-in">
                    <div class="charts-grid">
                        <div class="chart-container slide-in-left interactive-glow">
                            <div class="chart-header">
                                <h3><i class="fas fa-chart-pie"></i> توزيع الأحداث حسب الخطورة</h3>
                            </div>
                            <div class="chart-content">
                                <canvas id="severityChart"></canvas>
                            </div>
                        </div>
                        <div class="chart-container slide-in-right interactive-glow">
                            <div class="chart-header">
                                <h3><i class="fas fa-chart-area"></i> الأحداث خلال الأسبوع الماضي</h3>
                            </div>
                            <div class="chart-content">
                                <canvas id="timelineChart"></canvas>
                            </div>
                        </div>
                        <div class="chart-container slide-in-left interactive-glow">
                            <div class="chart-header">
                                <h3><i class="fas fa-chart-bar"></i> توزيع أنواع الأحداث</h3>
                            </div>
                            <div class="chart-content">
                                <canvas id="typeChart"></canvas>
                            </div>
                        </div>
                        <div class="chart-container slide-in-right interactive-glow">
                            <div class="chart-header">
                                <h3><i class="fas fa-clipboard-check"></i> حالة الأحداث</h3>
                            </div>
                            <div class="chart-content">
                                <canvas id="statusChart"></canvas>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            <!-- Risk Analysis Section -->
            <div class="content-section" id="risk-analysis-section">
                <section class="risk-analysis">
                    <div class="section-header">
                        <h2><i class="fas fa-exclamation-triangle"></i> تحليل المخاطر الأمنية</h2>
                    </div>

                    <!-- Risk Score -->
                    <div class="risk-score-container">
                        <div class="risk-score-card">
                            <div class="risk-score-header">
                                <h3>مؤشر المخاطر العام</h3>
                            </div>
                            <div class="risk-score-content">
                                <div class="risk-gauge">
                                    <canvas id="riskGauge"></canvas>
                                </div>
                                <div class="risk-details">
                                    <div class="risk-level" id="riskLevel">متوسط</div>
                                    <div class="risk-score" id="riskScore">65/100</div>
                                    <div class="risk-description" id="riskDescription">
                                        يوجد مخاطر أمنية تتطلب اهتماماً
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Risk Categories -->
                    <div class="risk-categories">
                        <div class="risk-category">
                            <div class="risk-category-header">
                                <h4><i class="fas fa-user-shield"></i> مخاطر الوصول غير المصرح</h4>
                                <span class="risk-badge high" id="accessRisk">عالي</span>
                            </div>
                            <div class="risk-category-content">
                                <div class="risk-progress">
                                    <div class="risk-progress-bar" style="width: 75%"></div>
                                </div>
                                <p>تم رصد محاولات وصول غير مصرح متعددة</p>
                            </div>
                        </div>

                        <div class="risk-category">
                            <div class="risk-category-header">
                                <h4><i class="fas fa-bug"></i> مخاطر البرمجيات الخبيثة</h4>
                                <span class="risk-badge medium" id="malwareRisk">متوسط</span>
                            </div>
                            <div class="risk-category-content">
                                <div class="risk-progress">
                                    <div class="risk-progress-bar" style="width: 45%"></div>
                                </div>
                                <p>مستوى معتدل من التهديدات البرمجية</p>
                            </div>
                        </div>

                        <div class="risk-category">
                            <div class="risk-category-header">
                                <h4><i class="fas fa-database"></i> مخاطر تسريب البيانات</h4>
                                <span class="risk-badge critical" id="dataRisk">حرج</span>
                            </div>
                            <div class="risk-category-content">
                                <div class="risk-progress">
                                    <div class="risk-progress-bar" style="width: 85%"></div>
                                </div>
                                <p>خطر عالي لتسريب البيانات الحساسة</p>
                            </div>
                        </div>

                        <div class="risk-category">
                            <div class="risk-category-header">
                                <h4><i class="fas fa-server"></i> مخاطر فشل الأنظمة</h4>
                                <span class="risk-badge low" id="systemRisk">منخفض</span>
                            </div>
                            <div class="risk-category-content">
                                <div class="risk-progress">
                                    <div class="risk-progress-bar" style="width: 25%"></div>
                                </div>
                                <p>الأنظمة تعمل بشكل مستقر</p>
                            </div>
                        </div>
                    </div>

                    <!-- Recommendations -->
                    <div class="recommendations">
                        <div class="section-header">
                            <h3><i class="fas fa-lightbulb"></i> التوصيات الأمنية</h3>
                        </div>
                        <div class="recommendations-list" id="recommendationsList">
                            <!-- Recommendations will be populated by JavaScript -->
                        </div>
                    </div>
                </section>
            </div>

            <!-- Event Types Management Section -->
            <div class="content-section" id="event-types-section">
                <section class="event-types-management">
                    <div class="section-header">
                        <h2><i class="fas fa-tags"></i> إدارة أنواع الأحداث</h2>
                        <button class="btn btn-primary" id="addEventTypeBtn">
                            <i class="fas fa-plus"></i>
                            إضافة نوع جديد
                        </button>
                    </div>

                    <div class="event-types-container">
                        <div class="event-types-grid" id="eventTypesGrid">
                            <!-- Event types will be rendered here -->
                        </div>
                    </div>
                </section>
            </div>

            <!-- Post Incident Review Section -->
            <div class="content-section" id="post-incident-review-section">
                <section class="post-incident-review">
                    <div class="section-header">
                        <h2><i class="fas fa-clipboard-check"></i> المراجعة ما بعد الحادث</h2>
                        <div class="header-actions">
                            <button class="btn btn-secondary" id="viewReviewsBtn">
                                <i class="fas fa-list"></i>
                                عرض المراجعات المحفوظة
                            </button>
                        </div>
                    </div>

                    <form class="post-incident-form" id="postIncidentForm">
                        <!-- Basic Information -->
                        <div class="form-section">
                            <h3><i class="fas fa-info-circle"></i> المعلومات الأساسية</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="reviewerName">الاسم</label>
                                    <input type="text" id="reviewerName" name="reviewerName" required>
                                </div>
                                <div class="form-group">
                                    <label for="reviewerDepartment">القسم</label>
                                    <input type="text" id="reviewerDepartment" name="reviewerDepartment" required>
                                </div>
                                <div class="form-group">
                                    <label for="reviewerManagement">الإدارة</label>
                                    <input type="text" id="reviewerManagement" name="reviewerManagement" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="incidentDate">التاريخ</label>
                                    <input type="date" id="incidentDate" name="incidentDate" required>
                                </div>
                                <div class="form-group">
                                    <label for="incidentTime">الوقت</label>
                                    <input type="time" id="incidentTime" name="incidentTime" required>
                                </div>
                                <div class="form-group">
                                    <label for="incidentLocation">المكان</label>
                                    <textarea id="incidentLocation" name="incidentLocation" rows="2" required></textarea>
                                </div>
                            </div>
                        </div>

                        <!-- Incident Details -->
                        <div class="form-section">
                            <h3><i class="fas fa-exclamation-triangle"></i> تفاصيل الحادث</h3>

                            <!-- Import Event Section -->
                            <div class="import-event-section">
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="eventSerialImport">استيراد حدث من السجل</label>
                                        <div class="import-controls">
                                            <div class="import-input-container">
                                                <input type="text" id="eventSerialImport" placeholder="أدخل رقم التسلسل (مثال: SEC-001)" class="import-input" autocomplete="off">
                                                <div class="event-suggestions" id="eventSuggestions"></div>
                                            </div>
                                            <button type="button" class="btn btn-info" id="importEventBtn">
                                                <i class="fas fa-download"></i>
                                                استيراد الحدث
                                            </button>
                                        </div>
                                        <small class="form-help">يمكنك استيراد تفاصيل حدث موجود من سجل الأحداث الأمنية</small>
                                    </div>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="incidentDescription">وصف الحادث</label>
                                    <textarea id="incidentDescription" name="incidentDescription" rows="4" required></textarea>
                                </div>
                                <div class="form-group">
                                    <label for="incidentRecordNumber">رقم الحادث في السجل</label>
                                    <input type="text" id="incidentRecordNumber" name="incidentRecordNumber" required>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="incidentType">نوع الحادث</label>
                                    <select id="incidentType" name="incidentType" required>
                                        <option value="">اختر نوع الحادث</option>
                                        <!-- سيتم تحميل الأنواع من النظام -->
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="incidentResponsible">المسؤول عن الحادث</label>
                                    <input type="text" id="incidentResponsible" name="incidentResponsible" required>
                                </div>
                            </div>
                        </div>

                        <!-- Employee Preparedness -->
                        <div class="form-section">
                            <h3><i class="fas fa-user-check"></i> تقييم الموظف</h3>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="employeePreparedness">استعداد الموظف للحادث</label>
                                    <select id="employeePreparedness" name="employeePreparedness" required>
                                        <option value="">اختر مستوى الاستعداد</option>
                                        <option value="prepared">مستعد</option>
                                        <option value="partial">جزئي</option>
                                        <option value="unprepared">غير مستعد</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="employeeResponse">كيفية استجابته للحادث</label>
                                    <select id="employeeResponse" name="employeeResponse" required>
                                        <option value="">اختر مستوى الاستجابة</option>
                                        <option value="poor">سيئ الاستجابة</option>
                                        <option value="partial">جزئي</option>
                                        <option value="complete">كامل</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Incident Analysis -->
                        <div class="form-section">
                            <h3><i class="fas fa-search"></i> تحليل الحادث</h3>
                            <div class="form-group">
                                <label for="apparentCauses">الأسباب الظاهرية للحادث</label>
                                <textarea id="apparentCauses" name="apparentCauses" rows="4" required></textarea>
                            </div>

                            <div class="form-group">
                                <label for="rootCauses">الأسباب الجذرية للحادث</label>
                                <textarea id="rootCauses" name="rootCauses" rows="4" required></textarea>
                            </div>

                            <div class="form-group">
                                <label for="contributingFactors">العوامل التي ساهمت في وقوع الحادث</label>
                                <textarea id="contributingFactors" name="contributingFactors" rows="4" required></textarea>
                            </div>
                        </div>

                        <!-- Recovery Plans -->
                        <div class="form-section">
                            <h3><i class="fas fa-redo"></i> خطط الاسترداد</h3>
                            <div class="form-group">
                                <label for="recoveryPlansActivated">خطط الاسترداد التي تم تفعيلها</label>
                                <textarea id="recoveryPlansActivated" name="recoveryPlansActivated" rows="4" required></textarea>
                            </div>

                            <div class="form-group">
                                <label for="recoveryPlansSuitability">مدى ملائمة خطط الاسترداد لمعالجة أثر الحادث</label>
                                <textarea id="recoveryPlansSuitability" name="recoveryPlansSuitability" rows="4" required></textarea>
                            </div>
                        </div>

                        <!-- Corrective Actions -->
                        <div class="form-section">
                            <h3><i class="fas fa-tools"></i> الإجراءات التصحيحية</h3>
                            <div class="form-group">
                                <label for="correctiveActions">الإجراءات التصحيحية التي تم اتخاذها لمنع وقوع حوادث مماثلة في المستقبل</label>
                                <textarea id="correctiveActions" name="correctiveActions" rows="4" required></textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="actionResponsible">المسؤول عن تنفيذ الإجراءات</label>
                                    <input type="text" id="actionResponsible" name="actionResponsible" required>
                                </div>
                                <div class="form-group">
                                    <label for="implementationPeriod">فترة تنفيذ هذه الإجراءات (الوقت المفضل للتنفيذ)</label>
                                    <input type="text" id="implementationPeriod" name="implementationPeriod" required>
                                </div>
                            </div>
                        </div>

                        <!-- Follow-up Mechanism -->
                        <div class="form-section">
                            <h3><i class="fas fa-eye"></i> آلية المتابعة</h3>
                            <div class="form-group">
                                <label for="followUpMechanism">آلية متابعة الإجراءات التصحيحية لضمان تنفيذها بشكل صحيح</label>
                                <textarea id="followUpMechanism" name="followUpMechanism" rows="4" required></textarea>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="followUpResponsible">المسؤول عن متابعة الإجراءات التصحيحية</label>
                                    <input type="text" id="followUpResponsible" name="followUpResponsible" required>
                                </div>
                                <div class="form-group">
                                    <label for="followUpTiming">توقيت إجراء المتابعة</label>
                                    <input type="text" id="followUpTiming" name="followUpTiming" required>
                                </div>
                            </div>
                        </div>

                        <!-- Lessons Learned -->
                        <div class="form-section">
                            <h3><i class="fas fa-graduation-cap"></i> الدروس المستفادة</h3>
                            <div class="form-group">
                                <label for="lessonsLearned">الدروس المستفادة من هذا الحادث</label>
                                <textarea id="lessonsLearned" name="lessonsLearned" rows="6" required></textarea>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ المراجعة
                            </button>
                            <button type="reset" class="btn btn-secondary">
                                <i class="fas fa-undo"></i> إعادة تعيين
                            </button>
                            <button type="button" class="btn btn-info" id="generateReportBtn">
                                <i class="fas fa-file-pdf"></i> إنشاء تقرير
                            </button>
                        </div>
                    </form>

                    <!-- Saved Reviews Section -->
                    <div class="saved-reviews-section" id="savedReviewsSection" style="display: none;">
                        <div class="section-header">
                            <h3><i class="fas fa-archive"></i> المراجعات المحفوظة</h3>
                            <button class="btn btn-primary" id="backToFormBtn">
                                <i class="fas fa-plus"></i>
                                إضافة مراجعة جديدة
                            </button>
                        </div>

                        <div class="reviews-table-container">
                            <table class="reviews-table">
                                <thead>
                                    <tr>
                                        <th>رقم الحادث</th>
                                        <th>اسم المراجع</th>
                                        <th>تاريخ الحادث</th>
                                        <th>نوع الحادث</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="reviewsTableBody">
                                    <!-- Reviews will be rendered here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>
            </div>

            <!-- User Management Section -->
            <div class="content-section" id="user-management-section">
                <section class="user-management">
                    <div class="section-header">
                        <h2><i class="fas fa-users"></i> إدارة المستخدمين</h2>
                        <button class="btn btn-primary" id="addUserBtn">
                            <i class="fas fa-user-plus"></i>
                            إضافة مستخدم جديد
                        </button>
                    </div>

                    <div class="users-grid" id="usersGrid">
                        <!-- Users will be populated here -->
                    </div>
                </section>
            </div>
        </main>

        <!-- Floating Action Button -->
        <button class="fab" id="scrollToTopBtn" title="العودة للأعلى">
            <i class="fas fa-arrow-up"></i>
        </button>
    <!-- زر تسجيل خروج إضافي في أسفل الصفحة -->
    <button class="btn btn-danger" id="logoutBtnFooter" style="position: fixed; left: 20px; bottom: 20px; z-index: 9999;">
        <i class="fas fa-sign-out-alt"></i>
    </button>
    <style>
    #logoutBtnFooter {
        width: 44px;
        height: 44px;
        border-radius: 50%;
        padding: 0;
        font-size: 18px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0,0,0,0.12);
    }
    #logoutBtnFooter i {
        margin: 0;
    }
    </style>
    </button>
    </div>

    <!-- Event Details Modal -->
    <div class="modal" id="eventModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>تفاصيل الحدث</h3>
                <button class="modal-close" id="modalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="modalBody">
                <!-- Event details will be populated here -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="editEventBtn">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-danger" id="deleteEventBtn">
                    <i class="fas fa-trash"></i> حذف
                </button>
                <button class="btn btn-secondary" id="closeModalBtn">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
    </div>

    <!-- User Management Modal -->
    <div class="modal" id="userModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="userModalTitle">إضافة مستخدم جديد</h3>
                <button class="modal-close" id="userModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <form class="user-form" id="userForm">
                    <div class="form-row">
                        <div class="form-group">
                            <label for="userFullName">الاسم الكامل</label>
                            <input type="text" id="userFullName" name="fullName" required>
                        </div>
                        <div class="form-group">
                            <label for="userUsername">اسم المستخدم</label>
                            <input type="text" id="userUsername" name="username" required>
                        </div>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="userEmail">البريد الإلكتروني</label>
                            <input type="email" id="userEmail" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="userRole">الدور</label>
                            <select id="userRole" name="role" required>
                                <option value="">اختر الدور</option>
                                <option value="admin">مدير النظام</option>
                                <option value="analyst">محلل أمني</option>
                                <option value="operator">مشغل</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="userPassword">كلمة المرور</label>
                        <input type="password" id="userPassword" name="password" required>
                    </div>
                    <div class="form-group">
                        <label>الصلاحيات</label>
                        <div class="permissions-grid">
                            <label class="permission-checkbox">
                                <input type="checkbox" name="permissions" value="read">
                                <span>قراءة</span>
                            </label>
                            <label class="permission-checkbox">
                                <input type="checkbox" name="permissions" value="write">
                                <span>كتابة</span>
                            </label>
                            <label class="permission-checkbox">
                                <input type="checkbox" name="permissions" value="delete">
                                <span>حذف</span>
                            </label>
                            <label class="permission-checkbox">
                                <input type="checkbox" name="permissions" value="view_analytics">
                                <span>عرض الإحصائيات</span>
                            </label>
                            <label class="permission-checkbox">
                                <input type="checkbox" name="permissions" value="manage_users">
                                <span>إدارة المستخدمين</span>
                            </label>
                            <label class="permission-checkbox">
                                <input type="checkbox" name="permissions" value="manage_system">
                                <span>إدارة النظام</span>
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="saveUserBtn">
                    <i class="fas fa-save"></i> حفظ
                </button>
                <button class="btn btn-secondary" id="cancelUserBtn">
                    <i class="fas fa-times"></i> إلغاء
                </button>
            </div>
        </div>
    </div>

    <!-- Event Type Modal -->
    <div class="modal" id="eventTypeModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="eventTypeModalTitle">إضافة نوع حدث جديد</h3>
                <button class="modal-close" onclick="securityManager.closeEventTypeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="eventTypeForm" onsubmit="securityManager.saveEventType(event)">
                <div class="form-group">
                    <label for="eventTypeKey">مفتاح النوع (بالإنجليزية)</label>
                    <input type="text" id="eventTypeKey" name="eventTypeKey" required
                           placeholder="مثال: malware, phishing, intrusion">
                    <small class="form-help">يستخدم في النظام ولا يمكن تغييره بعد الحفظ</small>
                </div>
                <div class="form-group">
                    <label for="eventTypeLabel">اسم النوع (بالعربية)</label>
                    <input type="text" id="eventTypeLabel" name="eventTypeLabel" required
                           placeholder="مثال: برمجية خبيثة، تصيد إلكتروني، اختراق">
                </div>
                <div class="form-group">
                    <label for="eventTypeDescription">وصف النوع</label>
                    <textarea id="eventTypeDescription" name="eventTypeDescription" rows="3"
                              placeholder="وصف مختصر لنوع الحدث..."></textarea>
                </div>
                <div class="form-group">
                    <label for="eventTypeIcon">أيقونة النوع</label>
                    <select id="eventTypeIcon" name="eventTypeIcon" required>
                        <option value="">اختر أيقونة</option>
                        <option value="fas fa-virus">🦠 فيروس/برمجية خبيثة</option>
                        <option value="fas fa-fishing">🎣 تصيد إلكتروني</option>
                        <option value="fas fa-user-secret">🕵️ اختراق</option>
                        <option value="fas fa-database">💾 تسريب بيانات</option>
                        <option value="fas fa-shield-alt">🛡️ خرق أمني</option>
                        <option value="fas fa-exclamation-triangle">⚠️ تهديد أمني</option>
                        <option value="fas fa-bug">🐛 ثغرة أمنية</option>
                        <option value="fas fa-lock">🔒 مشكلة تشفير</option>
                        <option value="fas fa-network-wired">🌐 مشكلة شبكة</option>
                        <option value="fas fa-server">🖥️ مشكلة خادم</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="eventTypeColor">لون النوع</label>
                    <select id="eventTypeColor" name="eventTypeColor" required>
                        <option value="">اختر لون</option>
                        <option value="#dc2626">🔴 أحمر (خطر عالي)</option>
                        <option value="#ea580c">🟠 برتقالي (تحذير)</option>
                        <option value="#d97706">🟡 أصفر (انتباه)</option>
                        <option value="#2563eb">🔵 أزرق (معلومات)</option>
                        <option value="#7c3aed">🟣 بنفسجي (خاص)</option>
                        <option value="#059669">🟢 أخضر (آمن)</option>
                        <option value="#6b7280">⚫ رمادي (عام)</option>
                    </select>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="securityManager.closeEventTypeModal()">
                        إلغاء
                    </button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i>
                        حفظ
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Review Details Modal -->
    <div class="modal" id="reviewModal">
        <div class="modal-content large-modal">
            <div class="modal-header">
                <h3><i class="fas fa-clipboard-check"></i> تفاصيل المراجعة</h3>
                <button class="modal-close" id="reviewModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body" id="reviewModalBody">
                <!-- Review details will be populated here -->
            </div>
            <div class="modal-footer">
                <button class="btn btn-info" id="printReviewBtn">
                    <i class="fas fa-print"></i> طباعة
                </button>
                <button class="btn btn-primary" id="editReviewFromModalBtn">
                    <i class="fas fa-edit"></i> تعديل
                </button>
                <button class="btn btn-secondary" id="closeReviewModalBtn">
                    <i class="fas fa-times"></i> إغلاق
                </button>
            </div>
        </div>
    </div>

    <!-- User Profile Modal -->
    <div class="modal" id="userProfileModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-edit"></i> الملف الشخصي</h3>
                <button class="modal-close" id="userProfileModalClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="profile-tabs">
                    <button class="tab-btn active" data-tab="profile-info">
                        <i class="fas fa-user"></i>
                        المعلومات الشخصية
                    </button>
                    <button class="tab-btn" data-tab="change-password">
                        <i class="fas fa-key"></i>
                        تغيير كلمة المرور
                    </button>
                </div>

                <!-- Profile Information Tab -->
                <div class="tab-content active" id="profile-info">
                    <form class="profile-form" id="profileInfoForm">
                        <div class="form-row">
                            <div class="form-group">
                                <label for="profileFullName">الاسم الكامل</label>
                                <input type="text" id="profileFullName" name="fullName" required>
                            </div>
                            <div class="form-group">
                                <label for="profileUsername">اسم المستخدم</label>
                                <input type="text" id="profileUsername" name="username" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="profileEmail">البريد الإلكتروني</label>
                            <input type="email" id="profileEmail" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="profileRole">الدور</label>
                            <input type="text" id="profileRole" name="role" readonly>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> حفظ التغييرات
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Change Password Tab -->
                <div class="tab-content" id="change-password">
                    <form class="password-form" id="changePasswordForm">
                        <div class="form-group">
                            <label for="currentPassword">كلمة المرور الحالية</label>
                            <div class="password-input-container">
                                <input type="password" id="currentPassword" name="currentPassword" required>
                                <button type="button" class="password-toggle" onclick="togglePasswordVisibility('currentPassword')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label for="newPassword">كلمة المرور الجديدة</label>
                            <div class="password-input-container">
                                <input type="password" id="newPassword" name="newPassword" required minlength="6">
                                <button type="button" class="password-toggle" onclick="togglePasswordVisibility('newPassword')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <small class="form-help">يجب أن تكون كلمة المرور 6 أحرف على الأقل</small>
                        </div>
                        <div class="form-group">
                            <label for="confirmPassword">تأكيد كلمة المرور الجديدة</label>
                            <div class="password-input-container">
                                <input type="password" id="confirmPassword" name="confirmPassword" required>
                                <button type="button" class="password-toggle" onclick="togglePasswordVisibility('confirmPassword')">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>
                        <div class="password-strength" id="passwordStrength">
                            <div class="strength-bar">
                                <div class="strength-fill"></div>
                            </div>
                            <span class="strength-text">قوة كلمة المرور</span>
                        </div>
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-key"></i> تغيير كلمة المرور
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
    <script src="auth.js"></script>
    <script src="demo-data.js"></script>
</body>
</html>
