/* تصغير زر تسجيل الخروج في القائمة العلوية */
#logoutBtn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    padding: 0;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.10);
    color: #fff;
    background: #dc3545;
    border: none;
    margin: 0 auto;
    transition: background 0.2s;
}
#logoutBtn:hover {
    background: #b91c1c;
}
#logoutBtn i {
    margin: 0;
}
/* إخفاء النص لذوي الاحتياجات الخاصة فقط */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0,0,0,0);
    border: 0;
}
/* CSS Variables for Enhanced Blue & Gray Theme */
:root {
    /* Enhanced Blue & Gray Color Palette */
    --primary-blue: #1e40af;
    --secondary-blue: #2563eb;
    --accent-blue: #3b82f6;
    --light-blue: #dbeafe;
    --dark-blue: #1d4ed8;
    --navy-blue: #1e3a8a;
    --primary-gray: #6b7280;
    --light-gray: #f8fafc;
    --medium-gray: #9ca3af;
    --dark-gray: #374151;
    --darker-gray: #1f2937;
    --white: #ffffff;
    --black: #111827;
    --success: #059669;
    --warning: #d97706;
    --danger: #dc2626;
    --info: #0284c7;

    /* Enhanced Blue & Gray Gradients */
    --gradient-primary: linear-gradient(135deg, #1e40af 0%, #2563eb 50%, #3b82f6 100%);
    --gradient-secondary: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
    --gradient-blue: linear-gradient(135deg, #1e3a8a 0%, #1e40af 30%, #2563eb 70%, #3b82f6 100%);
    --gradient-blue-light: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    --gradient-gray: linear-gradient(135deg, #374151 0%, #6b7280 100%);
    --gradient-success: linear-gradient(135deg, #059669 0%, #10b981 100%);
    --gradient-warning: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
    --gradient-danger: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);

    /* Background and Text */
    --bg-primary: #ffffff;
    --bg-secondary: #f8fafc;
    --bg-tertiary: #e5e7eb;
    --bg-glass: rgba(255, 255, 255, 0.25);
    --bg-card: rgba(255, 255, 255, 0.95);
    --text-primary: #1f2937;
    --text-secondary: #6b7280;
    --border-color: #d1d5db;
    --border-light: #e5e7eb;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Enhanced Blue & Gray Dark Theme */
[data-theme="dark"] {
    --bg-primary: #1f2937;
    --bg-secondary: #111827;
    --bg-tertiary: #374151;
    --bg-glass: rgba(31, 41, 55, 0.8);
    --bg-card: rgba(31, 41, 55, 0.95);
    --text-primary: #f9fafb;
    --text-secondary: #9ca3af;
    --border-color: #4b5563;
    --border-light: #374151;
    --light-gray: #374151;
    --medium-gray: #6b7280;
    --dark-gray: #9ca3af;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.4);
    --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.6);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.7);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.8);

    /* Enhanced gradients for dark theme */
    --gradient-glass: linear-gradient(135deg, rgba(31, 41, 55, 0.3) 0%, rgba(31, 41, 55, 0.1) 100%);
}

[data-theme="dark"] body {
    background-image:
        radial-gradient(circle at 25% 25%, rgba(30, 64, 175, 0.08) 0%, transparent 60%),
        radial-gradient(circle at 75% 75%, rgba(107, 114, 128, 0.08) 0%, transparent 60%),
        linear-gradient(135deg, rgba(30, 64, 175, 0.03) 0%, rgba(107, 114, 128, 0.03) 100%);
}

[data-theme="dark"] .app-container::before {
    background:
        radial-gradient(circle at 20% 80%, rgba(30, 64, 175, 0.05) 0%, transparent 60%),
        radial-gradient(circle at 80% 20%, rgba(107, 114, 128, 0.05) 0%, transparent 60%),
        radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.03) 0%, transparent 70%);
}

[data-theme="dark"] .app-container::after {
    background:
        linear-gradient(45deg, transparent 48%, rgba(30, 64, 175, 0.02) 49%, rgba(30, 64, 175, 0.02) 51%, transparent 52%),
        linear-gradient(-45deg, transparent 48%, rgba(107, 114, 128, 0.02) 49%, rgba(107, 114, 128, 0.02) 51%, transparent 52%);
}

/* Dark theme for main title section */
[data-theme="dark"] .main-title-section {
    background: var(--gradient-blue);
}

[data-theme="dark"] .header {
    background: rgba(31, 41, 55, 0.95);
    border-bottom-color: var(--border-color);
}

[data-theme="dark"] .main-nav {
    background: var(--bg-primary);
    border-color: var(--border-color);
}

[data-theme="dark"] .nav-btn {
    color: var(--text-primary);
}

[data-theme="dark"] .nav-btn:hover {
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
    color: var(--accent-blue);
    border-color: var(--accent-blue);
}

/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    font-size: 16px;
    background: var(--bg-secondary);
    background-image:
        radial-gradient(circle at 25% 25%, rgba(30, 64, 175, 0.06) 0%, transparent 60%),
        radial-gradient(circle at 75% 75%, rgba(107, 114, 128, 0.06) 0%, transparent 60%),
        linear-gradient(135deg, rgba(30, 64, 175, 0.02) 0%, rgba(107, 114, 128, 0.02) 100%);
    color: var(--text-primary);
    line-height: 1.7;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    overflow-x: hidden;
    position: relative;
}

.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
}

/* Enhanced animated background elements */
.app-container::before {
    content: '';
    position: fixed;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background:
        radial-gradient(circle at 20% 80%, rgba(30, 64, 175, 0.03) 0%, transparent 60%),
        radial-gradient(circle at 80% 20%, rgba(107, 114, 128, 0.03) 0%, transparent 60%),
        radial-gradient(circle at 50% 50%, rgba(59, 130, 246, 0.02) 0%, transparent 70%);
    animation: float 25s ease-in-out infinite;
    z-index: -1;
}

.app-container::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        linear-gradient(45deg, transparent 48%, rgba(30, 64, 175, 0.01) 49%, rgba(30, 64, 175, 0.01) 51%, transparent 52%),
        linear-gradient(-45deg, transparent 48%, rgba(107, 114, 128, 0.01) 49%, rgba(107, 114, 128, 0.01) 51%, transparent 52%);
    background-size: 60px 60px;
    animation: backgroundShift 30s linear infinite;
    z-index: -1;
    opacity: 0.5;
}

@keyframes float {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(20px, -15px) rotate(90deg); }
    50% { transform: translate(-15px, 25px) rotate(180deg); }
    75% { transform: translate(-25px, -20px) rotate(270deg); }
}

@keyframes backgroundShift {
    0% { transform: translate(0, 0); }
    100% { transform: translate(60px, 60px); }
}

/* Enhanced Interactive Elements */
.interactive-glow {
    position: relative;
    overflow: hidden;
}

.interactive-glow::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--gradient-blue);
    border-radius: inherit;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
    filter: blur(8px);
}

.interactive-glow:hover::before {
    opacity: 0.3;
}

/* Floating Action Button */
.fab {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    width: 60px;
    height: 60px;
    background: var(--gradient-blue);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: var(--shadow-xl);
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    border: none;
    backdrop-filter: blur(10px);
}

.fab:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: var(--shadow-2xl);
}

.fab:active {
    transform: scale(0.95);
}

/* Enhanced Scrollbar */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 6px;
    margin: 2px;
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-blue);
    border-radius: 6px;
    transition: all 0.3s ease;
    border: 2px solid var(--bg-secondary);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gradient-primary);
    box-shadow: 0 2px 8px rgba(30, 64, 175, 0.3);
    border-color: var(--bg-tertiary);
}

::-webkit-scrollbar-corner {
    background: var(--bg-secondary);
}

/* Firefox Scrollbar */
* {
    scrollbar-width: thin;
    scrollbar-color: var(--primary-blue) var(--bg-secondary);
}

/* Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, var(--bg-secondary) 25%, var(--bg-tertiary) 50%, var(--bg-secondary) 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
    border-radius: 8px;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

/* Confirmation Modal Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes fadeOut {
    from { opacity: 1; }
    to { opacity: 0; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Import Event Section Styles */
.import-event-section {
    background: var(--gradient-blue-light);
    border: 2px dashed var(--primary-blue);
    border-radius: 16px;
    padding: 2rem;
    margin-bottom: 2rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.import-event-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(30, 64, 175, 0.1), transparent);
    transition: left 0.6s ease;
}

.import-event-section:hover::before {
    left: 100%;
}

.import-event-section:hover {
    border-color: var(--secondary-blue);
    background: linear-gradient(135deg, rgba(219, 234, 254, 0.8) 0%, rgba(191, 219, 254, 0.8) 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.import-controls {
    display: flex;
    gap: 1rem;
    align-items: flex-end;
    position: relative;
    z-index: 1;
}

.import-input-container {
    flex: 1;
    position: relative;
}

.import-input {
    width: 100%;
    padding: 1.25rem 1.5rem;
    border: 2px solid var(--border-light);
    border-radius: 12px;
    font-size: 1.1rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-sm);
    font-weight: 500;
}

.import-input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 4px rgba(30, 64, 175, 0.15), var(--shadow);
    transform: translateY(-2px);
}

.import-input:hover {
    border-color: var(--accent-blue);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.import-input::placeholder {
    color: var(--text-secondary);
    font-style: italic;
    font-weight: 400;
}

/* Enhanced import button */
#importEventBtn {
    white-space: nowrap;
    min-width: 150px;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

#importEventBtn:hover {
    transform: translateY(-3px) scale(1.02);
}

#importEventBtn:active {
    transform: translateY(-1px) scale(0.98);
}

/* Form help text styling */
.form-help {
    display: block;
    margin-top: 0.5rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-style: italic;
    line-height: 1.4;
}

.form-help::before {
    content: '💡 ';
    margin-right: 0.25rem;
}

/* Success state for import section */
.import-event-section.success {
    border-color: var(--success);
    background: linear-gradient(135deg, rgba(5, 150, 105, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
}

.import-event-section.success::before {
    background: linear-gradient(90deg, transparent, rgba(5, 150, 105, 0.2), transparent);
}

/* Event Suggestions Dropdown */
.event-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-card);
    border: 2px solid var(--border-light);
    border-top: none;
    border-radius: 0 0 12px 12px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: var(--shadow-lg);
    backdrop-filter: blur(20px);
    display: none;
}

.event-suggestions.show {
    display: block;
    animation: slideDown 0.3s ease;
}

.suggestion-item {
    padding: 1rem 1.5rem;
    cursor: pointer;
    border-bottom: 1px solid var(--border-light);
    transition: all 0.2s ease;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-item:hover {
    background: var(--bg-secondary);
    color: var(--primary-blue);
}

.suggestion-item.selected {
    background: var(--primary-blue);
    color: white;
}

.suggestion-serial {
    font-weight: 600;
    color: var(--primary-blue);
    font-size: 0.9rem;
}

.suggestion-item.selected .suggestion-serial {
    color: rgba(255, 255, 255, 0.8);
}

.suggestion-title {
    flex: 1;
    margin-right: 1rem;
    font-size: 0.95rem;
    line-height: 1.3;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Highlight matching text in suggestions */
.suggestion-item mark {
    background: var(--accent-blue);
    color: white;
    padding: 0.1rem 0.2rem;
    border-radius: 3px;
    font-weight: 600;
}

.suggestion-item.selected mark {
    background: rgba(255, 255, 255, 0.3);
    color: white;
}

/* Dark theme for import section */
[data-theme="dark"] .import-event-section {
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.1) 0%, rgba(59, 130, 246, 0.1) 100%);
    border-color: var(--accent-blue);
}

[data-theme="dark"] .import-event-section:hover {
    background: linear-gradient(135deg, rgba(30, 64, 175, 0.15) 0%, rgba(59, 130, 246, 0.15) 100%);
}

/* Advanced Animation Effects */
.fade-in {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
}

.fade-in-delay-1 {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.1s forwards;
    opacity: 0;
}

.fade-in-delay-2 {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.2s forwards;
    opacity: 0;
}

.fade-in-delay-3 {
    animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) 0.3s forwards;
    opacity: 0;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Pulse Animation for Important Elements */
.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(30, 64, 175, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(30, 64, 175, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(30, 64, 175, 0);
    }
}

/* Bounce Animation for Buttons */
.bounce-in {
    animation: bounceIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* Slide In Animations */
.slide-in-right {
    animation: slideInRight 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Main Title Section Styles */
.main-title-section {
    background: var(--gradient-blue);
    color: white;
    padding: 2rem 0;
    box-shadow: var(--shadow-xl);
    position: relative;
    overflow: hidden;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.main-title-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(255, 255, 255, 0.05) 100%);
    pointer-events: none;
}

.main-title-section::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
}

.title-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: relative;
    z-index: 1;
    min-height: 100px;
}

/* Enhanced Navigation Header Styles */
.header {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-light);
    box-shadow: var(--shadow-lg);
    position: sticky;
    top: 0;
    z-index: 99;
    transition: all 0.3s ease;
}

.header-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 1;
    min-height: 70px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    padding: 0.5rem 0;
}

.logo i {
    font-size: 2rem;
    color: var(--light-blue);
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    transition: all 0.3s ease;
}

.main-title-section .logo-image {
    width: 60px;
    height: 60px;
    object-fit: contain;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.main-title-section .logo i {
    font-size: 2.5rem;
    color: var(--light-blue);
    filter: drop-shadow(0 3px 6px rgba(0, 0, 0, 0.3));
    transition: all 0.3s ease;
}

.logo-image:hover {
    transform: scale(1.08) rotate(2deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.4);
}

/* Fallback when logo image fails to load */
.logo-image:not([src]),
.logo-image[src=""] {
    display: none;
}

.logo-image:not([src]) + i,
.logo-image[src=""] + i {
    display: inline-block;
}

.main-title-section .logo h1 {
    font-size: 2.5rem;
    font-weight: 800;
    text-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.5px;
    background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    position: relative;
    line-height: 1.2;
    margin: 0;
    white-space: nowrap;
    transition: all 0.3s ease;
    cursor: default;
}

.main-title-section .logo:hover h1 {
    transform: scale(1.03);
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
}

.main-title-section .logo:hover i {
    transform: scale(1.15) rotate(10deg);
    color: #bfdbfe;
}

.main-title-section .logo:hover .logo-image {
    transform: scale(1.1) rotate(3deg);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.header-controls {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.theme-toggle {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.5rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 25px;
}

.user-menu {
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    color: var(--text-primary);
    border: 1px solid transparent;
    background: var(--bg-primary);
    box-shadow: var(--shadow-sm);
}

.user-menu:hover {
    background: var(--bg-secondary);
    border-color: var(--border-color);
    transform: translateY(-1px);
    box-shadow: var(--shadow);
}

.user-menu.active {
    background: var(--bg-secondary);
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
}

.user-menu i {
    font-size: 1.2rem;
    color: var(--primary-blue);
    transition: all 0.3s ease;
}

.user-menu:hover i {
    transform: scale(1.1);
    color: var(--accent-blue);
}

.user-dropdown {
    position: absolute;
    top: calc(100% + 0.5rem);
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-radius: 12px;
    box-shadow: var(--shadow-2xl);
    min-width: 220px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px) scale(0.95);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.user-dropdown::before {
    content: '';
    position: absolute;
    top: -6px;
    right: 1rem;
    width: 12px;
    height: 12px;
    background: var(--bg-primary);
    border: 1px solid var(--border-light);
    border-bottom: none;
    border-right: none;
    transform: rotate(45deg);
}

.user-menu.active .user-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0) scale(1);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.75rem 1rem;
    border: none;
    background: none;
    color: var(--text-primary);
    text-align: right;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.dropdown-item:hover {
    background: var(--bg-secondary);
    transform: translateX(-2px);
}

.dropdown-item:first-child {
    border-radius: 8px 8px 0 0;
}

.dropdown-item:last-child {
    border-radius: 0 0 8px 8px;
}

/* Special styling for logout button */
.dropdown-item#logoutBtn {
    color: var(--danger);
    border-top: 1px solid var(--border-light);
    margin-top: 0.25rem;
    font-weight: 600;
}

.dropdown-item#logoutBtn:hover {
    background: rgba(220, 38, 38, 0.1);
    color: var(--danger);
    transform: translateX(-3px);
}

.dropdown-item#logoutBtn i {
    color: var(--danger);
    transition: all 0.3s ease;
}

.dropdown-item#logoutBtn:hover i {
    transform: scale(1.1);
}

/* Enhanced Navigation Styles */
.main-nav {
    display: flex;
    gap: 0.75rem;
    align-items: center;
    background: var(--bg-primary);
    padding: 0.75rem;
    border-radius: 60px;
    backdrop-filter: blur(15px);
    border: 2px solid var(--border-light);
    box-shadow: var(--shadow-lg);
}

.nav-btn {
    background: transparent;
    backdrop-filter: blur(10px);
    border: 1px solid transparent;
    color: var(--text-primary);
    padding: 0.875rem 1.25rem;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    gap: 0.6rem;
    font-size: 0.95rem;
    font-weight: 600;
    position: relative;
    overflow: hidden;
    white-space: nowrap;
}

.nav-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.15), transparent);
    transition: left 0.6s ease;
}

.nav-btn:hover::before {
    left: 100%;
}

.nav-btn:hover {
    background: var(--gradient-blue-light);
    color: var(--primary-blue);
    transform: translateY(-2px) scale(1.02);
    box-shadow: 0 6px 20px rgba(30, 64, 175, 0.15);
    border-color: var(--accent-blue);
}

.nav-btn.active {
    background: var(--gradient-blue);
    color: white;
    box-shadow: 0 8px 25px rgba(30, 64, 175, 0.3);
    transform: translateY(-1px);
    border-color: var(--primary-blue);
    font-weight: 700;
}

.nav-btn.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 60%;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
    border-radius: 1px;
}

.nav-btn i {
    font-size: 1rem;
    transition: all 0.3s ease;
}

.nav-btn:hover i {
    transform: scale(1.1);
}

/* Content Sections */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* Main Content */
.main-content {
    flex: 1;
    max-width: 1400px; /* Increased max width for better use of space */
    margin: 0 auto;
    padding: 2.5rem; /* Increased padding */
    width: 100%;
}

/* Enhanced Modern Section Styles */
.event-input-section,
.events-log-section,
.post-incident-review {
    background: var(--bg-card);
    backdrop-filter: blur(25px);
    border-radius: 24px;
    padding: 3rem;
    margin-bottom: 2.5rem;
    box-shadow: var(--shadow-2xl);
    border: 1px solid var(--border-light);
    position: relative;
    overflow: hidden;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.event-input-section::before,
.events-log-section::before,
.post-incident-review::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 5px;
    background: var(--gradient-blue);
    border-radius: 24px 24px 0 0;
}

.event-input-section::after,
.events-log-section::after,
.post-incident-review::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-glass);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    border-radius: 24px;
}

.event-input-section:hover,
.events-log-section:hover,
.post-incident-review:hover {
    transform: translateY(-8px) scale(1.01);
    box-shadow: 0 35px 70px -15px rgba(0, 0, 0, 0.2);
    border-color: var(--accent-blue);
}

.event-input-section:hover::after,
.events-log-section:hover::after,
.post-incident-review:hover::after {
    opacity: 1;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    position: relative;
}

.section-header::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, var(--accent-blue) 0%, transparent 50%, var(--accent-blue) 100%);
    opacity: 0.3;
}

.section-header::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 80px;
    height: 4px;
    background: var(--gradient-blue);
    border-radius: 2px;
    box-shadow: 0 2px 8px rgba(30, 64, 175, 0.3);
}

.section-header h2 {
    background: var(--gradient-blue);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2.2rem;
    font-weight: 700;
    display: flex;
    align-items: center;
    gap: 1rem;
    position: relative;
    letter-spacing: 0.3px;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    line-height: 1.2;
    margin: 0;
}

.section-header h2 i {
    background: var(--gradient-blue);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
    transition: all 0.3s ease;
}

.section-header h2:hover i {
    transform: scale(1.1) rotate(5deg);
}

/* Form Styles */
.event-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
    letter-spacing: 0.2px;
    line-height: 1.3;
    margin-bottom: 0.6rem;
    display: block;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 1rem 1.25rem;
    border: 2px solid var(--border-light);
    border-radius: 12px;
    font-size: 1rem;
    background: var(--bg-card);
    color: var(--text-primary);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    box-shadow: var(--shadow-sm);
    backdrop-filter: blur(10px);
    line-height: 1.4;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 4px rgba(30, 64, 175, 0.15), var(--shadow-lg);
    transform: translateY(-3px) scale(1.01);
    background: var(--bg-primary);
}

.form-group input:hover,
.form-group select:hover,
.form-group textarea:hover {
    border-color: var(--accent-blue);
    box-shadow: var(--shadow);
    transform: translateY(-1px);
}

/* Special input types */
.form-group input[type="number"] {
    text-align: right;
}

.form-group input[readonly] {
    background-color: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: not-allowed;
    opacity: 0.8;
}

.form-help {
    display: block;
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
    font-style: italic;
    line-height: 1.4;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Enhanced Modern Button Styles */
.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 14px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    display: inline-flex;
    align-items: center;
    gap: 0.75rem;
    text-decoration: none;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-lg);
    border: 1px solid transparent;
    backdrop-filter: blur(10px);
    line-height: 1.2;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.25), transparent);
    transition: left 0.6s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.btn:active::after {
    width: 300px;
    height: 300px;
}

/* Enhanced Button States */
.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
    box-shadow: var(--shadow-sm) !important;
}

.btn:disabled:hover {
    transform: none !important;
    box-shadow: var(--shadow-sm) !important;
}

/* Button Loading State */
.btn.loading {
    position: relative;
    color: transparent !important;
    pointer-events: none;
}

.btn.loading::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top-color: rgba(255, 255, 255, 0.8);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

.btn-primary {
    background: var(--gradient-blue);
    color: white;
    box-shadow: 0 6px 20px rgba(30, 64, 175, 0.35);
    border-color: rgba(255, 255, 255, 0.1);
}

.btn-primary:hover {
    transform: translateY(-4px) scale(1.03);
    box-shadow: 0 12px 35px rgba(30, 64, 175, 0.45);
    border-color: rgba(255, 255, 255, 0.2);
}

.btn-secondary {
    background: var(--gradient-gray);
    color: white;
    box-shadow: 0 6px 20px rgba(107, 114, 128, 0.35);
    border-color: rgba(255, 255, 255, 0.1);
}

.btn-secondary:hover {
    transform: translateY(-4px) scale(1.03);
    box-shadow: 0 12px 35px rgba(107, 114, 128, 0.45);
    border-color: rgba(255, 255, 255, 0.2);
}

.btn-danger {
    background: var(--gradient-danger);
    color: white;
    box-shadow: 0 6px 20px rgba(220, 38, 38, 0.35);
    border-color: rgba(255, 255, 255, 0.1);
}

.btn-danger:hover {
    transform: translateY(-4px) scale(1.03);
    box-shadow: 0 12px 35px rgba(220, 38, 38, 0.45);
    border-color: rgba(255, 255, 255, 0.2);
}

.btn-info {
    background: linear-gradient(135deg, var(--secondary-blue), var(--accent-blue));
    color: white;
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.35);
    border-color: rgba(255, 255, 255, 0.1);
}

.btn-info:hover {
    transform: translateY(-4px) scale(1.03);
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.45);
    border-color: rgba(255, 255, 255, 0.2);
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
    margin-top: 1rem;
}

/* Log Controls */
.log-controls {
    display: flex;
    gap: 1rem;
    align-items: center;
    flex-wrap: wrap;
}

.excel-controls {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.search-input,
.filter-select {
    padding: 0.5rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 8px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.9rem;
}

.search-input {
    min-width: 250px;
}

/* Enhanced Modern Table Styles */
.events-table-container {
    overflow-x: auto;
    border-radius: 20px;
    border: 1px solid var(--border-light);
    box-shadow: var(--shadow-2xl);
    background: var(--bg-card);
    backdrop-filter: blur(20px);
    position: relative;
}

.events-table-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--gradient-blue);
    border-radius: 20px 20px 0 0;
}

.events-table {
    width: 100%;
    border-collapse: collapse;
    background: transparent;
    min-width: 1200px;
    position: relative;
}

/* Table column widths */
.events-table th:nth-child(1), /* Serial Number */
.events-table td:nth-child(1) {
    width: 120px;
    min-width: 120px;
}

.events-table th:nth-child(2), /* Title */
.events-table td:nth-child(2) {
    width: 200px;
    min-width: 200px;
}

.events-table th:nth-child(5), /* Responsible Person */
.events-table td:nth-child(5) {
    width: 180px;
    min-width: 180px;
}

.events-table th:nth-child(6), /* Costs */
.events-table td:nth-child(6) {
    width: 200px;
    min-width: 200px;
}

.events-table th:nth-child(9), /* Actions */
.events-table td:nth-child(9) {
    width: 120px;
    min-width: 120px;
}

.events-table th,
.events-table td {
    padding: 1.75rem 1.5rem;
    text-align: right;
    border-bottom: 1px solid var(--border-light);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-size: 1.05rem;
    line-height: 1.6;
    position: relative;
}

.events-table th {
    background: var(--gradient-blue);
    color: white;
    font-weight: 800;
    font-size: 1.15rem;
    position: sticky;
    top: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    border-bottom: 2px solid rgba(255, 255, 255, 0.25);
    letter-spacing: 0.3px;
    z-index: 10;
}

.events-table th::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
}

.events-table tbody tr {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.events-table tbody tr::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, var(--bg-secondary), rgba(30, 64, 175, 0.03));
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.events-table tbody tr:hover::before {
    opacity: 1;
}

.events-table tbody tr:hover {
    transform: scale(1.005);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.08);
    z-index: 1;
}

.events-table tbody tr:hover td {
    border-color: var(--accent-blue);
}

/* Modern Severity Badges */
.severity-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.95rem; /* Increased badge font size */
    font-weight: 800; /* Bolder badge text */
    text-align: center;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.severity-badge:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow);
}

.severity-low {
    background: var(--gradient-success);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.severity-medium {
    background: var(--gradient-warning);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.severity-high {
    background: var(--gradient-danger);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.severity-critical {
    background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.8; }
}

/* Dark theme severity badges */
[data-theme="dark"] .severity-low {
    background: #166534;
    color: #dcfce7;
}

[data-theme="dark"] .severity-medium {
    background: #92400e;
    color: #fef3c7;
}

[data-theme="dark"] .severity-high {
    background: #c53030;
    color: #fed7d7;
}

[data-theme="dark"] .severity-critical {
    background: #991b1b;
    color: #fecaca;
}

/* Modern Status Badges */
.status-badge {
    padding: 0.5rem 1rem;
    border-radius: 25px;
    font-size: 0.95rem; /* Increased status badge font size */
    font-weight: 800; /* Bolder status badge text */
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--shadow-sm);
    transition: all 0.3s ease;
}

.status-badge:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow);
}

.status-open {
    background: linear-gradient(135deg, var(--secondary-blue), var(--accent-blue));
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.status-investigating {
    background: var(--gradient-warning);
    color: white;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.status-resolved {
    background: #dcfce7;
    color: #166534;
}

.status-closed {
    background: #f3f4f6;
    color: #6b7280;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.action-btn {
    padding: 0.5rem 0.75rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem; /* Increased action button font size */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
}

.action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.action-btn:hover::before {
    left: 100%;
}

.action-btn:hover {
    transform: translateY(-2px) scale(1.05);
    box-shadow: var(--shadow);
}

/* Costs display in table */
.cost-item {
    font-size: 0.85rem;
    margin-bottom: 0.25rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    background: var(--bg-secondary);
}

.cost-item.direct {
    border-left: 3px solid var(--primary-blue);
}

.cost-item.indirect {
    border-left: 3px solid var(--warning-color);
}

.cost-item strong {
    color: var(--text-primary);
}

.no-costs {
    color: var(--text-secondary);
    font-style: italic;
    font-size: 0.9rem;
}

/* Alert styles */
.alert-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.alert {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 16px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 300px;
    animation: slideIn 0.3s ease-out;
}

.alert-error {
    background: #fee;
    color: #c53030;
    border-left: 4px solid #e53e3e;
}

.alert-success {
    background: #f0fff4;
    color: #2f855a;
    border-left: 4px solid #38a169;
}

.alert-info {
    background: #ebf8ff;
    color: #2b6cb0;
    border-left: 4px solid #3182ce;
}

.alert-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    margin-left: auto;
}

.alert-close:hover {
    background: rgba(0, 0, 0, 0.1);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Event Types Management */
.event-types-management {
    padding: 2rem;
}

.event-types-container {
    margin-top: 2rem;
}

.event-types-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
}

.event-type-card {
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.event-type-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-blue);
}

.event-type-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.event-type-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.event-type-info h3 {
    color: var(--text-primary);
    font-size: 1.2rem;
    font-weight: 600;
    margin: 0 0 0.25rem 0;
}

.event-type-key {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-family: 'Courier New', monospace;
    background: var(--bg-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    display: inline-block;
}

.event-type-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
    margin: 1rem 0;
}

.event-type-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 1rem;
}

.event-type-actions .action-btn {
    padding: 0.5rem;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.edit-type-btn {
    background: var(--primary-blue);
    color: white;
}

.edit-type-btn:hover {
    background: var(--secondary-blue);
}

.delete-type-btn {
    background: var(--danger-color);
    color: white;
}

.delete-type-btn:hover {
    background: #dc2626;
}

.event-type-usage {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: var(--bg-secondary);
    color: var(--text-secondary);
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.8rem;
}

.empty-event-types {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.empty-event-types i {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* Form help text */
.form-help {
    font-size: 0.8rem;
    color: var(--text-secondary);
    margin-top: 0.25rem;
    display: block;
}

/* Disabled button styles */
.action-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.action-btn:disabled:hover {
    transform: none;
    background: var(--danger-color);
}

.view-btn {
    background: linear-gradient(135deg, var(--secondary-blue), var(--accent-blue));
    color: white;
}

.edit-btn {
    background: var(--gradient-warning);
    color: white;
}

.delete-btn {
    background: var(--gradient-danger);
    color: white;
}

/* Enhanced Responsive Design */
@media (max-width: 1024px) {
    .main-content {
        padding: 2rem;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }
}

@media (max-width: 768px) {
    .main-title-section {
        padding: 1.5rem 0;
    }

    .title-content {
        padding: 0 1rem;
        flex-wrap: wrap;
        gap: 1rem;
        min-height: 80px;
    }

    .header {
        padding: 0.5rem 0;
    }

    .header-content {
        padding: 0 1rem;
        min-height: 60px;
    }

    .main-title-section .logo h1 {
        font-size: 2rem;
        letter-spacing: 0.3px;
    }

    .main-title-section .logo i {
        font-size: 2rem;
    }

    .main-title-section .logo-image {
        width: 50px;
        height: 50px;
    }

    .main-title-section .logo {
        gap: 0.75rem;
    }

    .main-nav {
        width: 100%;
        justify-content: center;
        padding: 0.5rem;
        gap: 0.5rem;
        margin: 0;
    }

    .nav-btn {
        flex: 1;
        max-width: 120px;
        padding: 0.875rem 1rem;
        font-size: 0.9rem;
    }

    .nav-btn span {
        display: none;
    }

    .nav-btn i {
        font-size: 1.25rem;
    }

    .main-content {
        padding: 1rem;
    }

    .event-input-section,
    .events-log-section,
    .post-incident-review {
        padding: 2rem;
        border-radius: 20px;
    }

    .section-header h2 {
        font-size: 1.8rem;
        gap: 0.75rem;
    }

    .section-header h2 i {
        font-size: 1.6rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .log-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .excel-controls {
        justify-content: center;
        margin-top: 0;
    }

    .search-input {
        min-width: auto;
    }

    .events-table {
        font-size: 0.95rem;
    }

    .events-table th,
    .events-table td {
        padding: 1rem 0.75rem;
    }

    .form-actions {
        flex-direction: column;
        gap: 1rem;
    }

    .btn {
        justify-content: center;
        width: 100%;
    }

    .stat-card {
        padding: 1.5rem;
        gap: 1rem;
    }

    .stat-content h3 {
        font-size: 2rem;
    }

    .fab {
        bottom: 1rem;
        left: 1rem;
        width: 50px;
        height: 50px;
        font-size: 1.25rem;
    }

    /* Import section responsive */
    .import-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .import-input {
        margin-bottom: 0.5rem;
    }

    #importEventBtn {
        min-width: auto;
        width: 100%;
        justify-content: center;
    }

    .import-event-section {
        padding: 1.5rem;
    }
}

@media (max-width: 480px) {
    .main-title-section {
        padding: 1rem 0;
    }

    .title-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
        min-height: auto;
    }

    .header-controls {
        order: 2;
    }

    .main-nav {
        flex-wrap: wrap;
        gap: 0.25rem;
        padding: 0.5rem;
    }

    .main-title-section .logo h1 {
        font-size: 1.8rem;
        letter-spacing: 0.2px;
        line-height: 1.1;
    }

    .main-title-section .logo i {
        font-size: 1.75rem;
    }

    .main-title-section .logo {
        gap: 0.5rem;
    }

    .main-title-section .logo-image {
        width: 45px;
        height: 45px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .section-header h2 {
        font-size: 1.6rem;
        gap: 0.5rem;
        letter-spacing: 0.2px;
    }

    .section-header h2 i {
        font-size: 1.4rem;
    }
}

/* Enhanced Modal Styles */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.6);
    z-index: 1000;
    backdrop-filter: blur(8px);
    transition: all 0.3s ease;
}

.modal.active {
    display: flex;
    align-items: center;
    justify-content: center;
    animation: modalFadeIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.modal-content {
    background: var(--bg-card);
    backdrop-filter: blur(25px);
    border-radius: 24px;
    max-width: 750px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: var(--shadow-2xl);
    border: 1px solid var(--border-light);
    animation: modalSlideIn 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    transform-origin: center;
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-blue);
    border-radius: 24px 24px 0 0;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(8px);
    }
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-50px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-content::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-blue);
    border-radius: 20px 20px 0 0;
}

.modal-header {
    padding: 2rem 2rem 1.5rem;
    border-bottom: 2px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: linear-gradient(135deg, var(--bg-primary), var(--bg-secondary));
    border-radius: 20px 20px 0 0;
}

.modal-header h3 {
    background: var(--gradient-blue);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 1.8rem; /* Increased modal header font size */
    font-weight: 800; /* Bolder modal header */
    margin: 0;
    letter-spacing: 0.3px;
}

.modal-close {
    background: var(--bg-glass);
    backdrop-filter: blur(10px);
    border: 1px solid var(--border-color);
    font-size: 1.25rem;
    cursor: pointer;
    color: var(--text-secondary);
    padding: 0.75rem;
    border-radius: 50%;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    background: var(--gradient-danger);
    color: white;
    transform: scale(1.1) rotate(90deg);
    box-shadow: var(--shadow);
}

.modal-body {
    padding: 2rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid var(--border-color);
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

/* Event Details Styles */
.event-detail {
    margin-bottom: 1rem;
}

.event-detail label {
    font-weight: 600;
    color: var(--text-secondary);
    display: block;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

.event-detail .value {
    color: var(--text-primary);
    font-size: 1rem;
    padding: 0.5rem 0;
}

.event-detail .detailed-description {
    background: var(--bg-secondary);
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid var(--primary-blue);
    line-height: 1.6;
    white-space: pre-wrap;
    max-height: 200px;
    overflow-y: auto;
}



/* Animations */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* Loading Spinner */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary-gray);
}

.empty-state h3 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

/* Modern Notification Styles */
.notification {
    position: fixed;
    top: 30px;
    right: 30px;
    padding: 1.5rem 2rem; /* Increased notification padding */
    border-radius: 16px;
    color: white;
    font-weight: 700; /* Bolder notification text */
    font-size: 1.1rem; /* Increased notification font size */
    z-index: 1100;
    animation: slideInRight 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    max-width: 500px; /* Increased max width */
    box-shadow: var(--shadow-xl);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    display: flex;
    align-items: center;
    gap: 1rem; /* Increased gap */
    line-height: 1.5;
}

.notification::before {
    content: '';
    width: 20px;
    height: 20px;
    border-radius: 50%;
    flex-shrink: 0;
}

.notification.success {
    background: var(--gradient-success);
}

.notification.success::before {
    background: rgba(255, 255, 255, 0.3);
}

.notification.error {
    background: var(--gradient-danger);
}

.notification.error::before {
    background: rgba(255, 255, 255, 0.3);
}

.notification.warning {
    background: var(--gradient-warning);
}

.notification.warning::before {
    background: rgba(255, 255, 255, 0.3);
}

.notification.info {
    background: var(--gradient-secondary);
}

.notification.info::before {
    background: rgba(255, 255, 255, 0.3);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Statistics Styles */
.stats-overview {
    margin-bottom: 2rem;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.stat-card {
    background: var(--bg-card);
    backdrop-filter: blur(25px);
    border: 1px solid var(--border-light);
    border-radius: 24px;
    padding: 2.5rem;
    display: flex;
    align-items: center;
    gap: 2rem;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 6px;
    height: 100%;
    background: var(--gradient-blue);
    border-radius: 0 6px 6px 0;
    box-shadow: 2px 0 8px rgba(30, 64, 175, 0.3);
}

.stat-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--gradient-glass);
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    border-radius: 24px;
}

.stat-card.critical::before {
    background: var(--gradient-danger);
    box-shadow: 2px 0 8px rgba(220, 38, 38, 0.3);
}

.stat-card.high::before {
    background: var(--gradient-warning);
    box-shadow: 2px 0 8px rgba(217, 119, 6, 0.3);
}

.stat-card.open::before {
    background: linear-gradient(135deg, var(--secondary-blue), var(--accent-blue));
    box-shadow: 2px 0 8px rgba(59, 130, 246, 0.3);
}

.stat-card:hover {
    transform: translateY(-10px) scale(1.03);
    box-shadow: var(--shadow-2xl);
    border-color: var(--accent-blue);
}

.stat-card:hover::after {
    opacity: 1;
}

.stat-icon {
    width: 80px; /* Increased icon container size */
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--gradient-blue);
    color: white;
    font-size: 2.25rem; /* Increased icon font size */
    box-shadow: var(--shadow);
    position: relative;
    overflow: hidden;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.stat-card.critical .stat-icon {
    background: var(--gradient-danger);
    color: white;
}

.stat-card.high .stat-icon {
    background: var(--gradient-warning);
    color: white;
}

.stat-card.open .stat-icon {
    background: linear-gradient(135deg, var(--secondary-blue), var(--accent-blue));
    color: white;
}

.stat-content h3 {
    font-size: 2.5rem; /* Increased stat number font size */
    font-weight: 800; /* Bolder stat numbers */
    color: var(--text-primary);
    margin: 0;
    letter-spacing: 0.5px;
}

.stat-content p {
    color: var(--text-secondary);
    margin: 0.5rem 0 0 0; /* Increased margin */
    font-size: 1.1rem; /* Increased stat label font size */
    font-weight: 600; /* Bolder stat labels */
}

/* Modern Charts Styles */
.charts-section {
    background: var(--bg-primary);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: var(--shadow-xl);
    border: 1px solid var(--border-color);
    position: relative;
    overflow: hidden;
}

.charts-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-blue);
    border-radius: 20px 20px 0 0;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
    gap: 2.5rem;
    margin-top: 2rem;
}

.chart-container {
    background: var(--bg-secondary);
    border-radius: 16px;
    padding: 2rem;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.chart-container:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
}

.chart-header {
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--light-blue);
}

.chart-header h3 {
    color: var(--primary-blue);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chart-content {
    position: relative;
    height: 300px;
}

.chart-content canvas {
    max-width: 100%;
    max-height: 100%;
}

/* Risk Analysis Styles */
.risk-analysis {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px var(--shadow);
    border: 1px solid var(--border-color);
}

.risk-score-container {
    margin-bottom: 2rem;
}

.risk-score-card {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 2rem;
    border: 1px solid var(--border-color);
}

.risk-score-header {
    text-align: center;
    margin-bottom: 1.5rem;
}

.risk-score-header h3 {
    color: var(--primary-blue);
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.risk-score-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    justify-content: center;
}

.risk-gauge {
    width: 200px;
    height: 200px;
}

.risk-details {
    text-align: center;
}

.risk-level {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--warning);
}

.risk-score {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-blue);
    margin-bottom: 1rem;
}

.risk-description {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Risk Categories */
.risk-categories {
    margin-bottom: 2rem;
}

.risk-category {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.risk-category:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px var(--shadow);
}

.risk-category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.risk-category-header h4 {
    color: var(--text-primary);
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.risk-badge {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-transform: uppercase;
}

.risk-badge.low {
    background: #dcfce7;
    color: #166534;
}

.risk-badge.medium {
    background: #fef3c7;
    color: #92400e;
}

.risk-badge.high {
    background: #fed7d7;
    color: #c53030;
}

.risk-badge.critical {
    background: #fecaca;
    color: #991b1b;
}

[data-theme="dark"] .risk-badge.low {
    background: #166534;
    color: #dcfce7;
}

[data-theme="dark"] .risk-badge.medium {
    background: #92400e;
    color: #fef3c7;
}

[data-theme="dark"] .risk-badge.high {
    background: #c53030;
    color: #fed7d7;
}

[data-theme="dark"] .risk-badge.critical {
    background: #991b1b;
    color: #fecaca;
}

.risk-category-content p {
    color: var(--text-secondary);
    margin: 0.5rem 0 0 0;
    font-size: 0.9rem;
}

.risk-progress {
    width: 100%;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.risk-progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--success), var(--warning), var(--danger));
    border-radius: 4px;
    transition: width 0.3s ease;
}

/* Recommendations */
.recommendations {
    background: var(--bg-secondary);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
}

.recommendations-list {
    margin-top: 1rem;
}

.recommendation-item {
    background: var(--bg-primary);
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    border-left: 4px solid var(--primary-blue);
    transition: all 0.3s ease;
}

.recommendation-item:hover {
    transform: translateX(5px);
    box-shadow: 0 2px 10px var(--shadow);
}

.recommendation-item.high-priority {
    border-left-color: var(--danger);
}

.recommendation-item.medium-priority {
    border-left-color: var(--warning);
}

.recommendation-item.low-priority {
    border-left-color: var(--success);
}

.recommendation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
}

.recommendation-title {
    font-weight: 600;
    color: var(--text-primary);
    font-size: 1rem;
}

.recommendation-priority {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
}

.recommendation-priority.high {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.recommendation-priority.medium {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.recommendation-priority.low {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.recommendation-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
    line-height: 1.5;
}

/* Modern Login Page Styles */
.login-body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
    font-family: 'Cairo', sans-serif;
}

[data-theme="dark"] .login-body {
    background: linear-gradient(135deg, #2d3748 0%, #1a202c 100%);
}

/* Animated Background */
.animated-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.floating-shapes {
    position: relative;
    width: 100%;
    height: 100%;
}

.shape {
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    animation: float 20s infinite linear;
}

.shape-1 {
    width: 80px;
    height: 80px;
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.shape-2 {
    width: 120px;
    height: 120px;
    top: 60%;
    left: 80%;
    animation-delay: 5s;
}

.shape-3 {
    width: 60px;
    height: 60px;
    top: 80%;
    left: 20%;
    animation-delay: 10s;
}

.shape-4 {
    width: 100px;
    height: 100px;
    top: 30%;
    left: 70%;
    animation-delay: 15s;
}

.shape-5 {
    width: 40px;
    height: 40px;
    top: 10%;
    left: 60%;
    animation-delay: 8s;
}

.shape-6 {
    width: 90px;
    height: 90px;
    top: 70%;
    left: 50%;
    animation-delay: 12s;
}

@keyframes float {
    0% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% {
        transform: translateY(-100px) rotate(180deg);
        opacity: 0.3;
    }
    100% {
        transform: translateY(0px) rotate(360deg);
        opacity: 0.7;
    }
}

/* Modern Login Container */
.login-container {
    display: flex;
    min-height: 100vh;
    width: 100%;
    position: relative;
    z-index: 2;
    max-width: 1400px;
    margin: 0 auto;
    box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);
    border-radius: 20px;
    overflow: hidden;
    backdrop-filter: blur(10px);
}

/* Left Side - Branding */
.login-branding {
    flex: 1;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    position: relative;
}

.brand-content {
    text-align: center;
    color: white;
}

.brand-logo {
    margin-bottom: 3rem;
}

.logo-circle {
    width: 120px;
    height: 120px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 2rem;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.logo-circle i {
    font-size: 3rem;
    color: white;
}

.logo-circle .logo-image {
    width: 80px;
    height: 80px;
    object-fit: contain;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.logo-circle .logo-image:hover {
    transform: scale(1.1);
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.3);
}

.brand-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    background: linear-gradient(45deg, #fff, #e2e8f0);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.brand-tagline {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 3rem;
    line-height: 1.6;
}

.features-list {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    text-align: right;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.feature-item:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateX(-5px);
}

.feature-item i {
    font-size: 1.5rem;
    color: #fbbf24;
    min-width: 30px;
}

.feature-item span {
    font-size: 1.1rem;
    font-weight: 500;
}

/* Right Side - Login Form */
.login-form-container {
    flex: 1;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.login-card {
    width: 100%;
    max-width: 450px;
    padding: 3rem;
}

[data-theme="dark"] .login-form-container {
    background: var(--bg-primary);
}

/* Modern Login Header */
.login-header {
    text-align: center;
    margin-bottom: 3rem;
}

.login-header h2 {
    color: var(--text-primary);
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: var(--text-secondary);
    font-size: 1rem;
    margin: 0;
    opacity: 0.8;
}

/* Modern Form Styles */
.modern-login-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.form-group {
    position: relative;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper input {
    width: 100%;
    padding: 1.2rem 3rem 1.2rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 1rem;
    background: var(--bg-secondary);
    color: var(--text-primary);
    transition: all 0.3s ease;
    direction: rtl;
    text-align: right;
}

.input-wrapper input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
    background: var(--bg-primary);
}

.input-wrapper input:focus + label,
.input-wrapper input:not(:placeholder-shown) + label {
    top: -8px;
    right: 12px;
    font-size: 0.8rem;
    color: var(--primary-blue);
    background: var(--bg-primary);
    padding: 0 8px;
}

.input-wrapper label {
    position: absolute;
    top: 50%;
    right: 3rem;
    transform: translateY(-50%);
    color: var(--text-secondary);
    font-size: 1rem;
    transition: all 0.3s ease;
    pointer-events: none;
    z-index: 1;
}

.input-icon {
    position: absolute;
    right: 1rem;
    color: var(--text-secondary);
    font-size: 1.1rem;
    z-index: 2;
    transition: all 0.3s ease;
}

.input-wrapper input:focus ~ .input-icon {
    color: var(--primary-blue);
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
    direction: rtl;
}

.input-group i {
    position: absolute;
    right: 1rem;
    color: var(--text-secondary);
    z-index: 2;
    pointer-events: none;
}

.input-group input {
    width: 100%;
    padding: 1rem 3rem 1rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: 12px;
    font-size: 1rem;
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.3s ease;
    direction: rtl;
    text-align: right;
}

.input-group input:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-group input::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
    text-align: right;
}

.input-group input:focus + i,
.input-group input:not(:placeholder-shown) + i {
    color: var(--primary-blue);
}

/* Fix for Safari and other browsers */
.input-group input:-webkit-autofill {
    -webkit-box-shadow: 0 0 0 30px var(--bg-primary) inset !important;
    -webkit-text-fill-color: var(--text-primary) !important;
}

.input-group input:-webkit-autofill:focus {
    -webkit-box-shadow: 0 0 0 30px var(--bg-primary) inset !important;
    -webkit-text-fill-color: var(--text-primary) !important;
}

/* Ensure proper text direction */
.input-group input[type="text"],
.input-group input[type="password"] {
    unicode-bidi: plaintext;
}

/* Password Toggle */
.password-toggle {
    position: absolute;
    left: 1rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    z-index: 3;
    top: 50%;
    transform: translateY(-50%);
}

.password-toggle:hover {
    color: var(--primary-blue);
    background: rgba(37, 99, 235, 0.1);
}

/* Modern Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 1rem 0;
}

.modern-checkbox {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    user-select: none;
}

.modern-checkbox input[type="checkbox"] {
    display: none;
}

.modern-checkbox .checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: 6px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    background: var(--bg-secondary);
}

.modern-checkbox input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
}

.modern-checkbox input[type="checkbox"]:checked + .checkmark i {
    color: white;
    font-size: 0.8rem;
}

.checkbox-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.forgot-password {
    color: var(--primary-blue);
    text-decoration: none;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.forgot-password:hover {
    text-decoration: underline;
    opacity: 0.8;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: var(--text-secondary);
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: 4px;
    position: relative;
    transition: all 0.3s ease;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background: var(--primary-blue);
    border-color: var(--primary-blue);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 12px;
    font-weight: bold;
}

/* Modern Login Button */
.modern-login-btn {
    background: linear-gradient(135deg, var(--primary-blue), var(--secondary-blue));
    color: white;
    border: none;
    padding: 1.2rem 2rem;
    border-radius: 12px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    position: relative;
    overflow: hidden;
    width: 100%;
    margin: 1rem 0;
}

.modern-login-btn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 10px 30px rgba(37, 99, 235, 0.4);
}

.modern-login-btn:active {
    transform: translateY(-1px);
}

.modern-login-btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
    transform: none;
}

.btn-text {
    flex: 1;
    text-align: center;
}

.btn-arrow {
    transition: all 0.3s ease;
}

.modern-login-btn:hover .btn-arrow {
    transform: translateX(-5px);
}

.btn-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.login-error {
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid var(--danger);
    color: var(--danger);
    padding: 1rem;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    animation: shake 0.5s ease-in-out;
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.login-footer {
    margin-top: 2rem;
    padding-top: 2rem;
    border-top: 1px solid var(--border-color);
}

/* Divider */
.divider {
    text-align: center;
    margin: 2rem 0;
    position: relative;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
}

.divider span {
    background: var(--bg-primary);
    padding: 0 1rem;
    color: var(--text-secondary);
    font-size: 0.9rem;
}



/* Login Background Animation */
.login-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1;
    overflow: hidden;
}

.security-icons {
    position: absolute;
    width: 100%;
    height: 100%;
}

.security-icons i {
    position: absolute;
    color: rgba(255, 255, 255, 0.1);
    font-size: 2rem;
    animation: float 6s ease-in-out infinite;
}

.security-icons i:nth-child(1) {
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.security-icons i:nth-child(2) {
    top: 60%;
    left: 20%;
    animation-delay: 1s;
}

.security-icons i:nth-child(3) {
    top: 30%;
    right: 15%;
    animation-delay: 2s;
}

.security-icons i:nth-child(4) {
    bottom: 30%;
    right: 25%;
    animation-delay: 3s;
}

.security-icons i:nth-child(5) {
    bottom: 20%;
    left: 30%;
    animation-delay: 4s;
}

.security-icons i:nth-child(6) {
    top: 50%;
    right: 40%;
    animation-delay: 5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
        opacity: 0.1;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 0.3;
    }
}

/* Login Theme Toggle */
.login-theme-toggle {
    position: fixed;
    top: 2rem;
    left: 2rem;
    z-index: 1000;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    padding: 0.75rem;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.login-theme-toggle:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* User Management Styles */
.user-management {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px var(--shadow);
    border: 1px solid var(--border-color);
}

.users-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-top: 1.5rem;
}

.user-card {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
    position: relative;
}

.user-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow);
}

.user-card.inactive {
    opacity: 0.6;
    border-color: var(--primary-gray);
}

.user-header {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-blue);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    font-weight: 600;
}

.user-info h4 {
    color: var(--text-primary);
    margin: 0 0 0.25rem 0;
    font-size: 1.1rem;
}

.user-info p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.9rem;
}

.user-role {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.role-admin {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.role-analyst {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.role-operator {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.user-permissions {
    margin-bottom: 1rem;
}

.user-permissions h5 {
    color: var(--text-primary);
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.permission-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
}

.permission-tag {
    background: var(--light-blue);
    color: var(--primary-blue);
    padding: 0.2rem 0.5rem;
    border-radius: 12px;
    font-size: 0.7rem;
    font-weight: 500;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
    justify-content: flex-end;
}

.user-action-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.3s ease;
}

.edit-user-btn {
    background: var(--info);
    color: white;
}

.delete-user-btn {
    background: var(--danger);
    color: white;
}

.toggle-user-btn {
    background: var(--warning);
    color: white;
}

.user-action-btn:hover {
    transform: scale(1.1);
    opacity: 0.8;
}

/* User Form Styles */
.user-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 0.5rem;
}

.permission-checkbox {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.permission-checkbox:hover {
    background: var(--bg-secondary);
}

.permission-checkbox input[type="checkbox"] {
    width: 16px;
    height: 16px;
    accent-color: var(--primary-blue);
}

.permission-checkbox span {
    font-size: 0.9rem;
    color: var(--text-primary);
}

/* Activity Log Styles */
.activity-log {
    background: var(--bg-primary);
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 20px var(--shadow);
    border: 1px solid var(--border-color);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.activity-item:hover {
    background: var(--bg-secondary);
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    flex-shrink: 0;
}

.activity-login {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success);
}

.activity-logout {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger);
}

.activity-user_management {
    background: rgba(37, 99, 235, 0.1);
    color: var(--primary-blue);
}

.activity-event {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning);
}

.activity-content {
    flex: 1;
}

.activity-description {
    color: var(--text-primary);
    font-weight: 500;
    margin-bottom: 0.25rem;
}

.activity-meta {
    color: var(--text-secondary);
    font-size: 0.8rem;
    display: flex;
    gap: 1rem;
}

/* Responsive Updates */
@media (max-width: 768px) {
    .user-dropdown {
        right: -50px;
        min-width: 180px;
    }

    .permissions-grid {
        grid-template-columns: 1fr;
    }

    .users-grid {
        grid-template-columns: 1fr;
    }

    .activity-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .activity-meta {
        flex-direction: column;
        gap: 0.25rem;
    }

    /* Modern Login Mobile Styles */
    .login-container {
        flex-direction: column;
        border-radius: 0;
        min-height: 100vh;
    }

    .login-branding {
        flex: none;
        padding: 2rem 1rem;
        min-height: 40vh;
    }

    .brand-content h1 {
        font-size: 2rem;
    }

    .brand-tagline {
        font-size: 1rem;
        margin-bottom: 2rem;
    }

    .logo-circle .logo-image {
        width: 60px;
        height: 60px;
    }

    .features-list {
        display: none;
    }

    .login-form-container {
        flex: 1;
        padding: 1rem;
    }

    .login-card {
        padding: 2rem 1rem;
    }

    .login-header h2 {
        font-size: 1.5rem;
    }

    .input-wrapper input {
        padding: 1rem 2.5rem 1rem 0.75rem;
        font-size: 0.9rem;
    }

    .input-icon {
        right: 0.75rem;
        font-size: 1rem;
    }

    .password-toggle {
        left: 0.75rem;
        padding: 0.25rem;
    }



    .form-options {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }
}

@media (max-width: 480px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .main-nav {
        order: -1;
        width: 100%;
        justify-content: center;
    }

    .nav-btn {
        flex: 1;
        justify-content: center;
        padding: 0.5rem;
        font-size: 0.8rem;
    }

    .nav-btn span {
        display: none;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .action-buttons {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 1rem;
    }

    .modal-footer {
        flex-direction: column;
    }

    .notification {
        right: 10px;
        left: 10px;
        max-width: none;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .risk-score-content {
        flex-direction: column;
        gap: 1rem;
    }
}

/* Post Incident Review Styles */
.post-incident-form {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.form-section {
    background: var(--bg-secondary);
    border-radius: 12px;
    padding: 1.5rem;
    border: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.form-section:hover {
    box-shadow: 0 2px 10px var(--shadow);
}

.form-section h3 {
    color: var(--primary-blue);
    font-size: 1.5rem; /* Increased form section header font size */
    font-weight: 700; /* Bolder form section headers */
    margin-bottom: 1.5rem;
    display: flex;
    align-items: center;
    gap: 0.75rem; /* Increased gap */
    padding-bottom: 0.75rem; /* Increased padding */
    border-bottom: 2px solid var(--light-blue);
    letter-spacing: 0.3px;
}

.form-section h3 i {
    font-size: 1.4rem; /* Increased form section icon size */
    color: var(--secondary-blue);
}

/* Three column layout for basic info */
.form-section .form-row:first-child {
    grid-template-columns: 1fr 1fr 1fr;
}

/* Enhanced form group styling for post incident review */
.post-incident-form .form-group {
    position: relative;
}

.post-incident-form .form-group label {
    font-weight: 700; /* Bolder post-incident form labels */
    color: var(--text-primary);
    font-size: 1.1rem; /* Increased post-incident form label font size */
    margin-bottom: 0.75rem; /* Increased margin */
    display: block;
    letter-spacing: 0.2px;
}

.post-incident-form .form-group input,
.post-incident-form .form-group select,
.post-incident-form .form-group textarea {
    width: 100%;
    padding: 1.125rem 1.25rem; /* Increased padding */
    border: 2px solid var(--border-color);
    border-radius: 12px; /* Increased border radius */
    font-size: 1.1rem; /* Increased font size */
    background: var(--bg-primary);
    color: var(--text-primary);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    font-family: 'Cairo', sans-serif;
    line-height: 1.5;
}

.post-incident-form .form-group input:focus,
.post-incident-form .form-group select:focus,
.post-incident-form .form-group textarea:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1);
    transform: translateY(-1px);
}

.post-incident-form .form-group textarea {
    resize: vertical;
    min-height: 120px;
    line-height: 1.6;
}

/* Special styling for select dropdowns */
.post-incident-form .form-group select {
    cursor: pointer;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-left: 3rem;
}

/* Enhanced form actions for post incident review */
.post-incident-form .form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-start;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 2px solid var(--border-color);
}

.btn-info {
    background: linear-gradient(135deg, var(--secondary-blue), var(--accent-blue));
    color: white;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
}

.btn-info:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

/* Saved Reviews Section */
.saved-reviews-section {
    margin-top: 2rem;
}

.saved-reviews-section .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid var(--border-color);
}

.saved-reviews-section h3 {
    color: var(--primary-blue);
    font-size: 1.5rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin: 0;
}

.reviews-table-container {
    overflow-x: auto;
    border-radius: 16px;
    border: 1px solid var(--border-color);
    box-shadow: var(--shadow-lg);
    background: var(--bg-primary);
    backdrop-filter: blur(10px);
}

.reviews-table {
    width: 100%;
    border-collapse: collapse;
    background: transparent;
    min-width: 800px;
}

.reviews-table th,
.reviews-table td {
    padding: 1.25rem 1rem;
    text-align: right;
    border-bottom: 1px solid var(--border-color);
    transition: all 0.3s ease;
}

.reviews-table th {
    background: var(--gradient-blue);
    color: white;
    font-weight: 700;
    position: sticky;
    top: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    border-bottom: 2px solid rgba(255, 255, 255, 0.2);
}

.reviews-table tbody tr {
    transition: all 0.3s ease;
}

.reviews-table tbody tr:hover {
    background: linear-gradient(90deg, var(--bg-secondary), rgba(30, 64, 175, 0.05));
    transform: scale(1.01);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.reviews-table .actions {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.empty-state {
    text-align: center;
    padding: 3rem 2rem;
    color: var(--text-secondary);
}

.empty-state i {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--primary-gray);
}

.empty-state p {
    font-size: 1.1rem;
    margin: 0;
}

.header-actions {
    display: flex;
    gap: 1rem;
}

/* Responsive design for post incident review */
@media (max-width: 768px) {
    .form-section .form-row:first-child {
        grid-template-columns: 1fr;
    }

    .post-incident-form .form-actions {
        flex-direction: column;
    }

    .post-incident-form .form-actions .btn {
        width: 100%;
        justify-content: center;
    }

    .saved-reviews-section .section-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .header-actions {
        justify-content: center;
    }

    .reviews-table .actions {
        flex-direction: column;
        gap: 0.25rem;
    }
}

/* User Profile Modal Styles */
.profile-tabs {
    display: flex;
    border-bottom: 2px solid var(--border-light);
    margin-bottom: 2rem;
    gap: 0.5rem;
}

.tab-btn {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    cursor: pointer;
    color: var(--text-secondary);
    font-weight: 500;
    border-radius: 8px 8px 0 0;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    position: relative;
}

.tab-btn:hover {
    background: var(--bg-secondary);
    color: var(--text-primary);
}

.tab-btn.active {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow);
}

.tab-btn.active::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.tab-content.active {
    display: block;
}

.profile-form,
.password-form {
    max-width: 500px;
}

.password-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.password-input-container input {
    padding-right: 3rem;
    flex: 1;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.password-toggle:hover {
    color: var(--primary-blue);
    background: var(--bg-secondary);
}

.password-strength {
    margin-top: 1rem;
    margin-bottom: 1rem;
}

.strength-bar {
    width: 100%;
    height: 6px;
    background: var(--border-light);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 0.5rem;
}

.strength-fill {
    height: 100%;
    width: 0%;
    transition: all 0.3s ease;
    border-radius: 3px;
}

.strength-fill.weak {
    width: 25%;
    background: var(--danger);
}

.strength-fill.fair {
    width: 50%;
    background: var(--warning);
}

.strength-fill.good {
    width: 75%;
    background: var(--info);
}

.strength-fill.strong {
    width: 100%;
    background: var(--success);
}

.strength-text {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.strength-text.weak {
    color: var(--danger);
}

.strength-text.fair {
    color: var(--warning);
}

.strength-text.good {
    color: var(--info);
}

.strength-text.strong {
    color: var(--success);
}

/* Profile form specific styles */
.profile-form .form-group input[readonly] {
    background: var(--bg-secondary);
    color: var(--text-secondary);
    cursor: not-allowed;
}

/* Responsive design for profile modal */
@media (max-width: 768px) {
    .profile-tabs {
        flex-direction: column;
        gap: 0;
    }

    .tab-btn {
        border-radius: 0;
        text-align: center;
    }

    .profile-form,
    .password-form {
        max-width: 100%;
    }

    .password-input-container {
        flex-direction: column;
        align-items: stretch;
    }

    .password-input-container input {
        padding-right: 1rem;
        margin-bottom: 0.5rem;
    }

    .password-toggle {
        position: static;
        align-self: flex-end;
        width: fit-content;
    }
}

/* Review Details Modal Styles */
.review-details {
    max-height: 70vh;
    overflow-y: auto;
    padding: 1rem;
}

.review-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--light-gray);
    border-radius: 8px;
    border-left: 4px solid var(--primary-blue);
}

.review-section h4 {
    color: var(--primary-blue);
    margin-bottom: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.review-section h4 i {
    color: var(--accent-blue);
}

.review-detail {
    margin-bottom: 1rem;
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 1rem;
    align-items: start;
}

.review-detail label {
    font-weight: 600;
    color: var(--dark-gray);
    font-size: 0.9rem;
}

.review-detail .value {
    background: var(--white);
    padding: 0.75rem;
    border-radius: 6px;
    border: 1px solid #e5e7eb;
    color: var(--darker-gray);
    line-height: 1.5;
}

.review-detail .value.description {
    white-space: pre-wrap;
    min-height: 60px;
}

/* Large modal for review details */
.large-modal .modal-content {
    max-width: 90vw;
    width: 1000px;
    max-height: 90vh;
}

/* Dark theme for review details */
[data-theme="dark"] .review-section {
    background: var(--darker-gray);
    border-left-color: var(--accent-blue);
}

[data-theme="dark"] .review-section h4 {
    color: var(--light-blue);
}

[data-theme="dark"] .review-detail label {
    color: var(--light-gray);
}

[data-theme="dark"] .review-detail .value {
    background: var(--dark-gray);
    border-color: #4b5563;
    color: var(--light-gray);
}

/* Responsive design for review details */
@media (max-width: 768px) {
    .review-detail {
        grid-template-columns: 1fr;
        gap: 0.5rem;
    }

    .review-detail label {
        font-size: 0.85rem;
    }

    .large-modal .modal-content {
        max-width: 95vw;
        width: auto;
        margin: 1rem;
    }

    .review-details {
        padding: 0.5rem;
    }

    .review-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }
}
