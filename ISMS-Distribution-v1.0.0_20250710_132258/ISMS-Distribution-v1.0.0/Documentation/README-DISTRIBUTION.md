# 📦 حزمة توزيع نظام إدارة أمن المعلومات

## 🎯 نظرة عامة
هذه حزمة شاملة لتوزيع نظام إدارة أمن المعلومات على الأجهزة المختلفة. تحتوي على عدة طرق للتثبيت والتشغيل لتناسب جميع البيئات.

## 🚀 التثبيت السريع (للمستخدمين النهائيين)

### الطريقة الأسهل - التثبيت التلقائي:
1. **شغل `quick-setup.bat`**
2. **اختر "1" للتثبيت السريع**
3. **انتظر حتى انتهاء التثبيت**
4. **شغل النظام من اختصار سطح المكتب**

### الطريقة البديلة - النسخة المحمولة:
1. **فك ضغط `ISMS-Portable-v1.0.0.zip`**
2. **شغل `تشغيل النظام.bat`**
3. **انتظر حتى يفتح المتصفح**

## 📋 محتويات الحزمة

### 🔧 ملفات التثبيت:
- `quick-setup.bat` - مثبت سريع وتفاعلي
- `install-simple.bat` - مثبت بسيط
- `uninstall-simple.bat` - إلغاء التثبيت
- `setup.py` - ملف setup لـ Python

### 📁 النسخة المحمولة:
- `ISMS-Portable-v1.0.0.zip` - نسخة محمولة كاملة
- `ISMS-Portable/` - مجلد النسخة المحمولة

### 🛠️ أدوات التطوير:
- `create-installer.py` - إنشاء مثبتات متقدمة
- `build-exe.py` - بناء ملف تنفيذي

### 📚 الدلائل:
- `دليل-التثبيت.md` - دليل شامل للتثبيت
- `دليل-التوزيع.md` - دليل التوزيع والنشر
- `README-DISTRIBUTION.md` - هذا الملف

## 🎯 طرق التثبيت المختلفة

### 1. للمستخدمين العاديين:
```bash
# شغل المثبت السريع
quick-setup.bat
```

### 2. للمطورين:
```bash
# تثبيت كحزمة Python
pip install .

# أو تثبيت مع أدوات التطوير
pip install .[dev]
```

### 3. للمؤسسات:
```bash
# تثبيت صامت
install-simple.bat /S

# أو استخدام Group Policy
# راجع دليل-التوزيع.md
```

## 🔑 بيانات الدخول الافتراضية

- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`
- **الرابط:** `http://localhost:8000`

⚠️ **مهم:** يرجى تغيير كلمة المرور بعد أول تسجيل دخول!

## 🌐 الوصول للنظام

### محلياً:
- http://localhost:8000
- http://127.0.0.1:8000

### من الشبكة:
- http://[عنوان-IP]:8000
- تأكد من إعدادات الجدار الناري

## 🔧 خيارات التشغيل

### 1. تشغيل عادي:
```bash
python start-server.py
```

### 2. كخدمة ويندوز:
```bash
# تثبيت الخدمة
python windows-service.py install

# تشغيل الخدمة
python windows-service.py start
```

### 3. كتطبيق ويندوز:
```bash
python windows-app.py
```

## 📊 متطلبات النظام

### الحد الأدنى:
- Windows 7/8/10/11 (64-bit)
- Python 3.7+ (للنسخة المحمولة)
- 2 GB RAM
- 100 MB مساحة قرص

### المستحسن:
- Windows 10/11
- Python 3.9+
- 4 GB RAM
- 500 MB مساحة قرص

## 🛡️ الأمان

### إعدادات افتراضية آمنة:
- تشفير الاتصالات
- حماية من XSS
- حماية من CSRF
- تسجيل العمليات

### توصيات إضافية:
- تغيير كلمات المرور
- تكوين الجدار الناري
- تحديث النظام بانتظام
- مراقبة السجلات

## 🔄 التحديث

### تحديث يدوي:
1. أوقف النظام
2. احتفظ بنسخة احتياطية
3. انسخ الملفات الجديدة
4. شغل النظام

### تحديث تلقائي:
- سيتم إضافة هذه الميزة في الإصدارات القادمة

## 🆘 استكشاف الأخطاء

### مشاكل شائعة:

#### Python غير مثبت:
```bash
# حمل Python من
https://python.org
# تأكد من تحديد "Add Python to PATH"
```

#### خطأ في الصلاحيات:
```bash
# شغل كمدير (Run as Administrator)
```

#### منفذ مشغول:
```bash
# النظام سيبحث عن منفذ آخر تلقائياً
# أو غير المنفذ في الإعدادات
```

#### ملفات مفقودة:
```bash
# تأكد من اكتمال فك الضغط/النسخ
# أعد تحميل الحزمة
```

## 📞 الدعم الفني

### الحصول على المساعدة:
1. راجع `دليل-التثبيت.md`
2. تحقق من سجلات الأخطاء
3. تواصل مع فريق أمن المعلومات

### الإبلاغ عن مشاكل:
- وصف المشكلة بالتفصيل
- أرفق لقطات شاشة
- اذكر نظام التشغيل والإصدار

## 📈 الميزات

### الحالية:
- ✅ واجهة ويب سهلة الاستخدام
- ✅ نظام مصادقة آمن
- ✅ إدارة السياسات والإجراءات
- ✅ تقارير وإحصائيات
- ✅ دعم اللغة العربية

### قادمة:
- 🔄 تحديث تلقائي
- 🔄 نسخ احتياطي تلقائي
- 🔄 تكامل مع Active Directory
- 🔄 تطبيق موبايل

## 📄 الترخيص
هذا النظام مطور لأغراض إدارة أمن المعلومات في المؤسسات.

---

## 🎉 شكراً لاستخدام نظام إدارة أمن المعلومات!

للمزيد من المعلومات، راجع الدلائل المرفقة أو تواصل مع فريق الدعم الفني.
