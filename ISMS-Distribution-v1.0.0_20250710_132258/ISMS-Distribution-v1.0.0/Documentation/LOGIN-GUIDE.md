# 🔐 دليل تسجيل الدخول - نظام إدارة أمن المعلومات
# Login Guide - Information Security Management System

## ✅ تم إصلاح مشكلة تسجيل الدخول!

### 🌐 روابط الوصول:
- **المحلي:** `http://localhost:8000/login.html`
- **الشبكة:** `http://*************:8000/login.html`

### 🔑 بيانات تسجيل الدخول المتاحة:

#### 1. المدير الرئيسي (Admin)
- **اسم المستخدم:** `admin`
- **كلمة المرور:** `admin123`
- **الصلاحيات:** جميع الصلاحيات (إدارة النظام، المستخدمين، التقارير)

#### 2. المحلل الأمني (Analyst)
- **اسم المستخدم:** `analyst`
- **كلمة المرور:** `analyst123`
- **الصلاحيات:** قراءة، كتابة، عرض التحليلات

#### 3. مشغل النظام (Operator)
- **اسم المستخدم:** `operator`
- **كلمة المرور:** `operator123`
- **الصلاحيات:** قراءة فقط

## 🚀 خطوات تسجيل الدخول:

### الخطوة 1: فتح النظام
1. تأكد من تشغيل الخادم: `python3 start-server.py`
2. افتح المتصفح على: `http://localhost:8000/login.html`

### الخطوة 2: إدخال البيانات
1. أدخل اسم المستخدم: `admin`
2. أدخل كلمة المرور: `admin123`
3. (اختياري) فعّل "تذكرني" للبقاء مسجلاً لمدة 30 يوم

### الخطوة 3: تسجيل الدخول
1. اضغط على زر "دخول"
2. انتظر رسالة "تم تسجيل الدخول بنجاح"
3. سيتم توجيهك تلقائياً للصفحة الرئيسية

## 🔧 المشاكل المحلولة:

### ✅ مشكلة JavaScript
- **المشكلة:** تعارض بين ملفي `login-script.js` و `auth.js`
- **الحل:** تم توحيد استخدام `auth.js` فقط

### ✅ مشكلة عناصر HTML
- **المشكلة:** عدم تطابق أسماء العناصر بين HTML و JavaScript
- **الحل:** تم توحيد أسماء العناصر (`loginError`, `errorMessage`)

### ✅ مشكلة المستخدمين الافتراضيين
- **المشكلة:** عدم وجود مستخدمين في النظام
- **الحل:** تم إنشاء مستخدمين افتراضيين تلقائياً

## 🛡️ ميزات الأمان:

### 🔒 تشفير كلمات المرور
- يتم تشفير كلمات المرور قبل التخزين
- استخدام خوارزمية hash بسيطة (للتطوير)

### ⏰ إدارة الجلسات
- جلسة عادية: 8 ساعات
- جلسة "تذكرني": 30 يوم
- إنهاء الجلسات المنتهية الصلاحية تلقائياً

### 📝 تسجيل الأنشطة
- تسجيل جميع عمليات تسجيل الدخول والخروج
- تتبع الأنشطة بالوقت والتاريخ
- حفظ معلومات المتصفح و IP

## 🔍 استكشاف الأخطاء:

### المشكلة: "اسم المستخدم أو كلمة المرور غير صحيح"
**الحلول:**
1. تأكد من استخدام البيانات الصحيحة:
   - `admin` / `admin123`
   - `analyst` / `analyst123`
   - `operator` / `operator123`

2. تأكد من عدم وجود مسافات إضافية
3. تحقق من حالة الأحرف (case-sensitive)

### المشكلة: لا يحدث شيء عند الضغط على "دخول"
**الحلول:**
1. افتح أدوات المطور (F12)
2. تحقق من وجود أخطاء JavaScript في Console
3. تأكد من تحميل ملف `auth.js` بنجاح
4. أعد تحميل الصفحة (Ctrl+F5)

### المشكلة: "حدث خطأ أثناء تسجيل الدخول"
**الحلول:**
1. امسح بيانات المتصفح (localStorage)
2. أعد تشغيل الخادم
3. تحقق من وجود ملف `index.html`

## 🔄 إعادة تعيين النظام:

إذا واجهت مشاكل مستمرة:

```javascript
// افتح Console في المتصفح (F12) واكتب:
localStorage.clear();
sessionStorage.clear();
location.reload();
```

أو أعد تشغيل الخادم:
```bash
# أوقف الخادم (Ctrl+C)
# ثم شغله مرة أخرى
python3 start-server.py
```

## 📞 الدعم الفني:

إذا استمرت المشاكل:
1. تحقق من ملف `ACCESS-LINKS.txt`
2. راجع ملف `NETWORK-SETUP.md`
3. شغّل `python3 network-config.py` لفحص الشبكة
4. تأكد من أن المنفذ 8000 غير مستخدم

## 🎯 الخطوات التالية:

بعد تسجيل الدخول بنجاح:
1. ستصل للصفحة الرئيسية للنظام
2. يمكنك استكشاف الميزات المختلفة
3. تغيير كلمة المرور من إعدادات المستخدم
4. إضافة مستخدمين جدد (للمدير فقط)

---

**✅ النظام جاهز للاستخدام!**  
**تم إصلاح جميع مشاكل تسجيل الدخول**
