#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء حزمة توزيع شاملة لنظام إدارة أمن المعلومات
Create Complete Distribution Package for ISMS
"""

import os
import sys
import shutil
import zipfile
import json
from datetime import datetime
from pathlib import Path

class DistributionPackager:
    def __init__(self):
        self.version = "1.0.0"
        self.package_name = f"ISMS-Distribution-v{self.version}"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # الملفات الأساسية للتوزيع
        self.core_files = [
            # ملفات الويب
            'index.html', 'login.html', 'styles.css', 'login-styles.css',
            'script.js', 'login-script.js', 'auth.js', 'demo-data.js',
            
            # ملفات Python
            'start-server.py', 'windows-service.py', 'windows-app.py',
            
            # الموارد
            'cyber-security.ico', 'logo.jpg', 'manifest.json',
            
            # ملفات التثبيت
            'quick-setup.bat', 'install-simple.bat', 'uninstall-simple.bat',
            'setup.py', 'verify-installation.py',
            
            # الدلائل
            'README-DISTRIBUTION.md', 'دليل-التثبيت.md', 'دليل-التوزيع.md'
        ]
        
        # الملفات الاختيارية
        self.optional_files = [
            'network-config.py', 'sw.js', 'windows-app-settings.json',
            'network-config.json', 'README.md', 'SYSTEM-STATUS.md'
        ]
        
        # أدوات التطوير
        self.dev_tools = [
            'create-installer.py', 'build-exe.py', 'create-distribution-package.py'
        ]
    
    def create_package_structure(self):
        """إنشاء هيكل حزمة التوزيع"""
        print("📁 إنشاء هيكل حزمة التوزيع...")
        
        # إنشاء المجلد الرئيسي
        if os.path.exists(self.package_name):
            shutil.rmtree(self.package_name)
        
        os.makedirs(self.package_name)
        
        # إنشاء المجلدات الفرعية
        subdirs = [
            'Core',           # الملفات الأساسية
            'Tools',          # أدوات التثبيت
            'Documentation',  # الدلائل
            'Optional',       # الملفات الاختيارية
            'Development'     # أدوات التطوير
        ]
        
        for subdir in subdirs:
            os.makedirs(os.path.join(self.package_name, subdir))
        
        print(f"✅ تم إنشاء هيكل الحزمة: {self.package_name}")
        return True
    
    def copy_core_files(self):
        """نسخ الملفات الأساسية"""
        print("📋 نسخ الملفات الأساسية...")
        
        core_dir = os.path.join(self.package_name, 'Core')
        copied_count = 0
        
        for file in self.core_files:
            if os.path.exists(file):
                shutil.copy2(file, core_dir)
                copied_count += 1
                print(f"   ✅ {file}")
            else:
                print(f"   ⚠️  {file} - غير موجود")
        
        print(f"📊 تم نسخ {copied_count} ملف أساسي")
        return True
    
    def copy_optional_files(self):
        """نسخ الملفات الاختيارية"""
        print("📋 نسخ الملفات الاختيارية...")
        
        optional_dir = os.path.join(self.package_name, 'Optional')
        copied_count = 0
        
        for file in self.optional_files:
            if os.path.exists(file):
                shutil.copy2(file, optional_dir)
                copied_count += 1
                print(f"   ✅ {file}")
        
        print(f"📊 تم نسخ {copied_count} ملف اختياري")
        return True
    
    def copy_tools(self):
        """نسخ أدوات التثبيت"""
        print("🔧 نسخ أدوات التثبيت...")
        
        tools_dir = os.path.join(self.package_name, 'Tools')
        
        # نسخ أدوات التثبيت
        install_tools = [
            'quick-setup.bat', 'install-simple.bat', 'uninstall-simple.bat',
            'setup.py', 'verify-installation.py'
        ]
        
        for tool in install_tools:
            if os.path.exists(tool):
                shutil.copy2(tool, tools_dir)
                print(f"   ✅ {tool}")
        
        # نسخ النسخة المحمولة إذا كانت موجودة
        portable_zip = f"ISMS-Portable-v{self.version}.zip"
        if os.path.exists(portable_zip):
            shutil.copy2(portable_zip, tools_dir)
            print(f"   ✅ {portable_zip}")
        
        return True
    
    def copy_development_tools(self):
        """نسخ أدوات التطوير"""
        print("🛠️  نسخ أدوات التطوير...")
        
        dev_dir = os.path.join(self.package_name, 'Development')
        
        for tool in self.dev_tools:
            if os.path.exists(tool):
                shutil.copy2(tool, dev_dir)
                print(f"   ✅ {tool}")
        
        return True
    
    def copy_documentation(self):
        """نسخ الدلائل"""
        print("📚 نسخ الدلائل...")
        
        docs_dir = os.path.join(self.package_name, 'Documentation')
        
        # البحث عن جميع ملفات الدلائل
        doc_files = []
        for file in os.listdir('.'):
            if file.endswith('.md') and os.path.isfile(file):
                doc_files.append(file)
        
        for doc in doc_files:
            shutil.copy2(doc, docs_dir)
            print(f"   ✅ {doc}")
        
        return True
    
    def create_package_info(self):
        """إنشاء ملف معلومات الحزمة"""
        print("📋 إنشاء ملف معلومات الحزمة...")
        
        package_info = {
            "name": "نظام إدارة أمن المعلومات",
            "name_en": "Information Security Management System",
            "version": self.version,
            "build_date": datetime.now().isoformat(),
            "description": "حزمة توزيع شاملة لنظام إدارة أمن المعلومات",
            "requirements": {
                "python": "3.7+",
                "os": "Windows 7/8/10/11",
                "ram": "2GB",
                "disk": "100MB"
            },
            "contents": {
                "core_files": len(self.core_files),
                "optional_files": len(self.optional_files),
                "tools": "Installation and verification tools",
                "documentation": "Complete user and admin guides"
            },
            "installation": {
                "quick": "Run quick-setup.bat",
                "portable": "Extract ISMS-Portable.zip",
                "manual": "Run install-simple.bat"
            },
            "default_credentials": {
                "username": "admin",
                "password": "admin123",
                "url": "http://localhost:8000"
            }
        }
        
        info_file = os.path.join(self.package_name, 'package-info.json')
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(package_info, f, ensure_ascii=False, indent=2)
        
        print("✅ تم إنشاء ملف معلومات الحزمة")
        return True
    
    def create_main_installer(self):
        """إنشاء مثبت رئيسي للحزمة"""
        print("🚀 إنشاء المثبت الرئيسي...")
        
        installer_content = f'''@echo off
chcp 65001 > nul
title تثبيت {self.package_name}

echo.
echo ==========================================
echo    نظام إدارة أمن المعلومات
echo    حزمة التوزيع الشاملة
echo    الإصدار: {self.version}
echo ==========================================
echo.

echo 📦 مرحباً بك في مثبت نظام إدارة أمن المعلومات
echo.
echo 🔍 اختر طريقة التثبيت:
echo.
echo [1] تثبيت سريع (مستحسن)
echo [2] تثبيت مخصص
echo [3] النسخة المحمولة
echo [4] التحقق من النظام
echo [5] عرض معلومات الحزمة
echo [0] خروج
echo.
set /p choice="اختر رقم (0-5): "

if "%choice%"=="1" goto :quick_install
if "%choice%"=="2" goto :custom_install
if "%choice%"=="3" goto :portable
if "%choice%"=="4" goto :verify
if "%choice%"=="5" goto :info
if "%choice%"=="0" goto :exit
goto :invalid

:quick_install
echo.
echo 🚀 بدء التثبيت السريع...
cd Core
call ..\Tools\quick-setup.bat
goto :end

:custom_install
echo.
echo 🔧 التثبيت المخصص...
cd Core
call ..\Tools\install-simple.bat
goto :end

:portable
echo.
echo 📁 النسخة المحمولة...
cd Tools
if exist "ISMS-Portable-v{self.version}.zip" (
    echo ✅ فك ضغط النسخة المحمولة...
    powershell -Command "Expand-Archive -Path 'ISMS-Portable-v{self.version}.zip' -DestinationPath '../ISMS-Portable' -Force"
    echo 🎉 تم فك الضغط بنجاح!
    echo 📍 المسار: ISMS-Portable
) else (
    echo ❌ ملف النسخة المحمولة غير موجود
)
goto :end

:verify
echo.
echo 🔍 التحقق من النظام...
cd Core
python ..\Tools\verify-installation.py
goto :end

:info
echo.
echo 📋 معلومات الحزمة...
type package-info.json
goto :end

:invalid
echo ❌ اختيار غير صحيح
goto :end

:exit
echo 👋 شكراً لاستخدام نظام إدارة أمن المعلومات
exit /b 0

:end
echo.
echo 📋 معلومات مهمة:
echo    👤 اسم المستخدم: admin
echo    🔒 كلمة المرور: admin123
echo    🌐 الرابط: http://localhost:8000
echo.
pause
'''
        
        installer_file = os.path.join(self.package_name, 'تثبيت-النظام.bat')
        with open(installer_file, 'w', encoding='utf-8') as f:
            f.write(installer_content)
        
        print("✅ تم إنشاء المثبت الرئيسي")
        return True
    
    def create_readme(self):
        """إنشاء ملف README للحزمة"""
        print("📄 إنشاء ملف README...")
        
        readme_content = f"""# 📦 {self.package_name}

## نظرة عامة
حزمة توزيع شاملة لنظام إدارة أمن المعلومات تحتوي على جميع الملفات والأدوات المطلوبة للتثبيت والتشغيل.

## 🚀 التثبيت السريع
1. شغل `تثبيت-النظام.bat`
2. اختر "تثبيت سريع"
3. اتبع التعليمات

## 📁 محتويات الحزمة

### Core/
الملفات الأساسية للنظام (HTML, CSS, JS, Python)

### Tools/
أدوات التثبيت والتحقق

### Documentation/
الدلائل والوثائق

### Optional/
ملفات إضافية اختيارية

### Development/
أدوات التطوير والبناء

## 🔑 بيانات الدخول
- اسم المستخدم: admin
- كلمة المرور: admin123
- الرابط: http://localhost:8000

## 📞 الدعم الفني
راجع ملفات الدلائل في مجلد Documentation

---
**الإصدار:** {self.version}  
**تاريخ البناء:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        readme_file = os.path.join(self.package_name, 'README.md')
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        print("✅ تم إنشاء ملف README")
        return True
    
    def create_zip_package(self):
        """إنشاء ملف مضغوط للحزمة"""
        print("📦 إنشاء الملف المضغوط...")
        
        zip_filename = f"{self.package_name}_{self.timestamp}.zip"
        
        with zipfile.ZipFile(zip_filename, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(self.package_name):
                for file in files:
                    file_path = os.path.join(root, file)
                    arcname = os.path.relpath(file_path, '.')
                    zipf.write(file_path, arcname)
        
        print(f"✅ تم إنشاء الملف المضغوط: {zip_filename}")
        return zip_filename
    
    def create_distribution_package(self):
        """إنشاء حزمة التوزيع الكاملة"""
        print("🚀 بدء إنشاء حزمة التوزيع الشاملة")
        print("=" * 60)
        
        try:
            # إنشاء هيكل الحزمة
            self.create_package_structure()
            
            # نسخ الملفات
            self.copy_core_files()
            self.copy_optional_files()
            self.copy_tools()
            self.copy_development_tools()
            self.copy_documentation()
            
            # إنشاء ملفات إضافية
            self.create_package_info()
            self.create_main_installer()
            self.create_readme()
            
            # إنشاء الملف المضغوط
            zip_file = self.create_zip_package()
            
            print("\n🎉 تم إنشاء حزمة التوزيع بنجاح!")
            print(f"📁 مجلد الحزمة: {self.package_name}")
            print(f"📦 الملف المضغوط: {zip_file}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء حزمة التوزيع: {e}")
            return False

def main():
    """الدالة الرئيسية"""
    packager = DistributionPackager()
    success = packager.create_distribution_package()
    
    if success:
        print("\n📋 الخطوات التالية:")
        print("1. اختبر الحزمة على جهاز آخر")
        print("2. وزع الملف المضغوط")
        print("3. قدم الدعم للمستخدمين")
        return 0
    else:
        print("\n❌ فشل في إنشاء حزمة التوزيع")
        return 1

if __name__ == "__main__":
    sys.exit(main())
