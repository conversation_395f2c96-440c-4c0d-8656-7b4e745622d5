@echo off
chcp 65001 > nul
title تثبيت سريع - نظام إدارة أمن المعلومات

echo.
echo ==========================================
echo    تثبيت سريع - نظام إدارة أمن المعلومات
echo    Quick Setup - ISMS
echo ==========================================
echo.

echo 🔍 التحقق من Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت على النظام
    echo 📥 يرجى تحميل Python من: https://python.org
    echo ⚠️  تأكد من تحديد "Add Python to PATH" أثناء التثبيت
    pause
    exit /b 1
)

echo ✅ Python متوفر

echo.
echo 📦 اختر طريقة التثبيت:
echo.
echo [1] تثبيت سريع (مستحسن)
echo [2] إنشاء نسخة محمولة
echo [3] بناء ملف تنفيذي
echo [4] إنشاء جميع أنواع المثبتات
echo [5] تشغيل النظام مباشرة (بدون تثبيت)
echo.
set /p choice="اختر رقم (1-5): "

if "%choice%"=="1" goto :quick_install
if "%choice%"=="2" goto :portable
if "%choice%"=="3" goto :build_exe
if "%choice%"=="4" goto :full_setup
if "%choice%"=="5" goto :run_direct
goto :invalid_choice

:quick_install
echo.
echo 🚀 بدء التثبيت السريع...
echo.

echo 📦 تثبيت المتطلبات...
pip install pywin32 requests >nul 2>&1

echo 📁 إنشاء مجلد التثبيت...
set "INSTALL_DIR=%USERPROFILE%\ISMS"
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 📋 نسخ الملفات...
copy "*.html" "%INSTALL_DIR%\" >nul 2>&1
copy "*.css" "%INSTALL_DIR%\" >nul 2>&1
copy "*.js" "%INSTALL_DIR%\" >nul 2>&1
copy "*.ico" "%INSTALL_DIR%\" >nul 2>&1
copy "*.jpg" "%INSTALL_DIR%\" >nul 2>&1
copy "*.json" "%INSTALL_DIR%\" >nul 2>&1
copy "*.py" "%INSTALL_DIR%\" >nul 2>&1
copy "*.md" "%INSTALL_DIR%\" >nul 2>&1

echo 🔗 إنشاء اختصار على سطح المكتب...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\نظام إدارة أمن المعلومات.lnk'); $Shortcut.TargetPath = 'python'; $Shortcut.Arguments = '\"%INSTALL_DIR%\start-server.py\"'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.IconLocation = '%INSTALL_DIR%\cyber-security.ico'; $Shortcut.Save()"

echo 📝 إنشاء ملف تشغيل...
(
echo @echo off
echo chcp 65001 ^> nul
echo title نظام إدارة أمن المعلومات
echo cd /d "%INSTALL_DIR%"
echo python start-server.py
echo pause
) > "%INSTALL_DIR%\تشغيل النظام.bat"

echo.
echo ✅ تم التثبيت بنجاح!
echo 📍 مسار التثبيت: %INSTALL_DIR%
echo 🖥️  تم إنشاء اختصار على سطح المكتب
echo.
goto :run_option

:portable
echo.
echo 📁 إنشاء النسخة المحمولة...
python create-installer.py portable
goto :end

:build_exe
echo.
echo 🔨 بناء الملف التنفيذي...
python create-installer.py exe
goto :end

:full_setup
echo.
echo 📦 إنشاء جميع أنواع المثبتات...
python create-installer.py all
goto :end

:run_direct
echo.
echo 🚀 تشغيل النظام مباشرة...
echo ⚠️  سيتم تشغيل النظام بدون تثبيت
echo.
python start-server.py
goto :end

:run_option
echo 🚀 هل تريد تشغيل النظام الآن؟ (Y/N)
set /p run_choice=
if /i "%run_choice%"=="Y" (
    echo.
    echo 🌐 جاري تشغيل النظام...
    cd /d "%INSTALL_DIR%"
    python start-server.py
)
goto :end

:invalid_choice
echo ❌ اختيار غير صحيح
goto :end

:end
echo.
echo 📋 معلومات مهمة:
echo    👤 اسم المستخدم: admin
echo    🔒 كلمة المرور: admin123
echo    🌐 الرابط: http://localhost:8000
echo.
pause
