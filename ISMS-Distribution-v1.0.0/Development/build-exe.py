#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
بناء ملف تنفيذي لويندوز
Build Windows Executable for ISMS
"""

import os
import sys
import subprocess
import shutil

def install_pyinstaller():
    """تثبيت PyInstaller"""
    print("📦 تثبيت PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("✅ تم تثبيت PyInstaller بنجاح")
        return True
    except subprocess.CalledProcessError:
        print("❌ فشل في تثبيت PyInstaller")
        return False

def create_spec_file():
    """إنشاء ملف spec لـ PyInstaller"""
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['windows-app.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('*.html', '.'),
        ('*.css', '.'),
        ('*.js', '.'),
        ('*.jpg', '.'),
        ('*.png', '.'),
        ('*.ico', '.'),
        ('*.json', '.'),
        ('*.md', '.'),
        ('*.txt', '.'),
        ('start-server.py', '.'),
    ],
    hiddenimports=[],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='ISMS-Windows',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='cyber-security.ico'
)
'''
    
    with open('isms-windows.spec', 'w', encoding='utf-8') as f:
        f.write(spec_content)
    
    print("✅ تم إنشاء ملف isms-windows.spec")

def build_executable():
    """بناء الملف التنفيذي"""
    print("🔨 بناء الملف التنفيذي...")
    try:
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--onefile",
            "--windowed",
            "--name=ISMS-Windows",
            "--icon=cyber-security.ico",
            "--add-data=*.html;.",
            "--add-data=*.css;.",
            "--add-data=*.js;.",
            "--add-data=*.jpg;.",
            "--add-data=*.png;.",
            "--add-data=*.ico;.",
            "--add-data=*.json;.",
            "--add-data=*.md;.",
            "--add-data=*.txt;.",
            "--add-data=start-server.py;.",
            "windows-app.py"
        ]
        
        subprocess.check_call(cmd)
        print("✅ تم بناء الملف التنفيذي بنجاح")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ فشل في بناء الملف التنفيذي: {e}")
        return False

def create_installer():
    """إنشاء مثبت ويندوز"""
    installer_script = '''
; نص تثبيت نظام إدارة أمن المعلومات
; ISMS Windows Installer Script

[Setup]
AppName=نظام إدارة أمن المعلومات
AppVersion=1.0.0
AppPublisher=Information Security Team
AppPublisherURL=https://github.com/your-repo
AppSupportURL=https://github.com/your-repo/issues
AppUpdatesURL=https://github.com/your-repo/releases
DefaultDirName={autopf}\\ISMS
DefaultGroupName=نظام إدارة أمن المعلومات
AllowNoIcons=yes
LicenseFile=
OutputDir=installer
OutputBaseFilename=ISMS-Windows-Setup
SetupIconFile=cyber-security.ico
Compression=lzma
SolidCompression=yes
WizardStyle=modern
ArchitecturesAllowed=x64
ArchitecturesInstallIn64BitMode=x64
PrivilegesRequired=admin
MinVersion=6.1sp1

[Languages]
Name: "arabic"; MessagesFile: "compiler:Languages\\Arabic.isl"
Name: "english"; MessagesFile: "compiler:Default.isl"

[Tasks]
Name: "desktopicon"; Description: "{cm:CreateDesktopIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked
Name: "quicklaunchicon"; Description: "{cm:CreateQuickLaunchIcon}"; GroupDescription: "{cm:AdditionalIcons}"; Flags: unchecked; OnlyBelowVersion: 6.1
Name: "installservice"; Description: "تثبيت كخدمة ويندوز (يعمل تلقائياً عند بدء التشغيل)"; GroupDescription: "خيارات إضافية"; Flags: unchecked

[Files]
Source: "dist\\ISMS-Windows.exe"; DestDir: "{app}"; Flags: ignoreversion
Source: "*.html"; DestDir: "{app}"; Flags: ignoreversion
Source: "*.css"; DestDir: "{app}"; Flags: ignoreversion
Source: "*.js"; DestDir: "{app}"; Flags: ignoreversion
Source: "*.jpg"; DestDir: "{app}"; Flags: ignoreversion
Source: "*.png"; DestDir: "{app}"; Flags: ignoreversion
Source: "*.ico"; DestDir: "{app}"; Flags: ignoreversion
Source: "*.json"; DestDir: "{app}"; Flags: ignoreversion
Source: "*.md"; DestDir: "{app}"; Flags: ignoreversion
Source: "*.txt"; DestDir: "{app}"; Flags: ignoreversion
Source: "start-server.py"; DestDir: "{app}"; Flags: ignoreversion
Source: "windows-service.py"; DestDir: "{app}"; Flags: ignoreversion
Source: "windows-app.py"; DestDir: "{app}"; Flags: ignoreversion

[Icons]
Name: "{group}\\نظام إدارة أمن المعلومات"; Filename: "{app}\\ISMS-Windows.exe"
Name: "{group}\\تشغيل الخادم"; Filename: "{app}\\start-server.py"
Name: "{group}\\إدارة الخدمة"; Filename: "{app}\\windows-service.py"
Name: "{group}\\{cm:UninstallProgram,نظام إدارة أمن المعلومات}"; Filename: "{uninstallexe}"
Name: "{autodesktop}\\نظام إدارة أمن المعلومات"; Filename: "{app}\\ISMS-Windows.exe"; Tasks: desktopicon
Name: "{userappdata}\\Microsoft\\Internet Explorer\\Quick Launch\\نظام إدارة أمن المعلومات"; Filename: "{app}\\ISMS-Windows.exe"; Tasks: quicklaunchicon

[Registry]
Root: HKLM; Subkey: "SOFTWARE\\ISMS"; ValueType: string; ValueName: "InstallPath"; ValueData: "{app}"; Flags: uninsdeletekey
Root: HKLM; Subkey: "SOFTWARE\\ISMS"; ValueType: string; ValueName: "Version"; ValueData: "1.0.0"; Flags: uninsdeletekey

[Run]
Filename: "{app}\\ISMS-Windows.exe"; Description: "{cm:LaunchProgram,نظام إدارة أمن المعلومات}"; Flags: nowait postinstall skipifsilent
Filename: "python"; Parameters: """{app}\\windows-service.py"" install"; WorkingDir: "{app}"; Description: "تثبيت خدمة ويندوز"; Flags: runhidden; Tasks: installservice
Filename: "python"; Parameters: """{app}\\windows-service.py"" start"; WorkingDir: "{app}"; Description: "تشغيل خدمة ويندوز"; Flags: runhidden; Tasks: installservice

[UninstallRun]
Filename: "python"; Parameters: """{app}\\windows-service.py"" stop"; WorkingDir: "{app}"; Flags: runhidden
Filename: "python"; Parameters: """{app}\\windows-service.py"" uninstall"; WorkingDir: "{app}"; Flags: runhidden
'''

    with open('isms-installer.iss', 'w', encoding='utf-8') as f:
        f.write(installer_script)

    print("✅ تم إنشاء ملف المثبت isms-installer.iss")
    print("📝 لإنشاء المثبت، استخدم Inno Setup مع هذا الملف")

def main():
    """الدالة الرئيسية"""
    print("🪟 بناء تطبيق ويندوز لنظام إدارة أمن المعلومات")
    print("=" * 60)
    
    # التحقق من وجود الملفات المطلوبة
    required_files = ['windows-app.py', 'start-server.py']
    missing_files = [f for f in required_files if not os.path.exists(f)]
    
    if missing_files:
        print(f"❌ ملفات مفقودة: {', '.join(missing_files)}")
        return False
    
    # تثبيت PyInstaller
    if not install_pyinstaller():
        return False
    
    # إنشاء ملف spec
    create_spec_file()
    
    # بناء الملف التنفيذي
    if not build_executable():
        return False
    
    # إنشاء مثبت
    create_installer()
    
    print("\n🎉 تم الانتهاء بنجاح!")
    print("📁 الملف التنفيذي: dist/ISMS-Windows.exe")
    print("📦 ملف المثبت: isms-installer.iss")
    print("\n📋 الخطوات التالية:")
    print("1. اختبر الملف التنفيذي: dist/ISMS-Windows.exe")
    print("2. لإنشاء مثبت، استخدم Inno Setup مع ملف isms-installer.iss")
    
    return True

if __name__ == "__main__":
    main()
