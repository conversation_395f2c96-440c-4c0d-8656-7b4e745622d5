@echo off
chcp 65001 > nul
title إلغاء تثبيت نظام إدارة أمن المعلومات

echo.
echo ==========================================
echo    إلغاء تثبيت نظام إدارة أمن المعلومات
echo ==========================================
echo.

set "INSTALL_DIR=%ProgramFiles%\ISMS"

echo ⚠️  هل أنت متأكد من إلغاء التثبيت؟ (Y/N)
set /p choice=
if /i not "%choice%"=="Y" goto :end

echo 🛑 إيقاف الخدمات...
python "%INSTALL_DIR%\windows-service.py" stop > nul 2>&1
python "%INSTALL_DIR%\windows-service.py" uninstall > nul 2>&1

echo 🗑️  حذف الملفات...
if exist "%INSTALL_DIR%" rmdir /s /q "%INSTALL_DIR%"

echo 🔗 حذف الاختصارات...
del "%USERPROFILE%\Desktop\نظام إدارة أمن المعلومات.lnk" > nul 2>&1

echo 📝 حذف من النظام...
reg delete "HKLM\SOFTWARE\ISMS" /f > nul 2>&1

echo.
echo ✅ تم إلغاء التثبيت بنجاح!

:end
pause
