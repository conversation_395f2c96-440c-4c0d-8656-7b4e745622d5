@echo off
chcp 65001 > nul
title تثبيت نظام إدارة أمن المعلومات

echo.
echo ==========================================
echo    تثبيت نظام إدارة أمن المعلومات
echo    الإصدار: 1.0.0
echo ==========================================
echo.

set "INSTALL_DIR=%ProgramFiles%\ISMS"

echo 📁 إنشاء مجلد التثبيت...
if not exist "%INSTALL_DIR%" mkdir "%INSTALL_DIR%"

echo 📋 نسخ الملفات...
copy "*.html" "%INSTALL_DIR%\" > nul 2>&1
copy "*.css" "%INSTALL_DIR%\" > nul 2>&1
copy "*.js" "%INSTALL_DIR%\" > nul 2>&1
copy "*.ico" "%INSTALL_DIR%\" > nul 2>&1
copy "*.jpg" "%INSTALL_DIR%\" > nul 2>&1
copy "*.json" "%INSTALL_DIR%\" > nul 2>&1
copy "*.py" "%INSTALL_DIR%\" > nul 2>&1

echo 🔗 إنشاء الاختصارات...
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\Desktop\نظام إدارة أمن المعلومات.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\start-server.py'; $Shortcut.Save()"

echo 📝 تسجيل في النظام...
reg add "HKLM\SOFTWARE\ISMS" /v "InstallPath" /t REG_SZ /d "%INSTALL_DIR%" /f > nul 2>&1
reg add "HKLM\SOFTWARE\ISMS" /v "Version" /t REG_SZ /d "1.0.0" /f > nul 2>&1

echo.
echo ✅ تم التثبيت بنجاح!
echo 📍 مسار التثبيت: %INSTALL_DIR%
echo 🖥️  تم إنشاء اختصار على سطح المكتب
echo.
echo 🚀 هل تريد تشغيل النظام الآن؟ (Y/N)
set /p choice=
if /i "%choice%"=="Y" (
    cd /d "%INSTALL_DIR%"
    python start-server.py
)

pause
