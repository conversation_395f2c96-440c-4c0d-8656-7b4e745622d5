// Demo Data for Security Events Management System
// This file contains sample security events for testing purposes

const demoEvents = [
    {
        id: 1,
        serialNumber: "INC-2024-001",
        title: "محاولة اختراق خادم الويب الرئيسي",
        severity: "critical",
        type: "intrusion",
        date: "2024-01-15T14:30:00",
        description: "تم رصد محاولات متعددة للوصول غير المصرح به إلى خادم الويب الرئيسي من عناوين IP مشبوهة. تم تسجيل أكثر من 500 محاولة دخول فاشلة خلال 10 دقائق.",
        detailedDescription: "تفاصيل الحدث:\n\n1. الوقت: 14:30 - بدء رصد النشاط المشبوه\n2. المصدر: عناوين IP متعددة من دول مختلفة\n3. الهدف: صفحة تسجيل الدخول الإدارية\n4. النمط: هجوم Brute Force منسق\n\nالإجراءات المتخذة:\n- حظر عناوين IP المشبوهة فوراً\n- تفعيل نظام الحماية المتقدم\n- إشعار فريق الأمان\n- مراجعة سجلات الوصول\n\nالتوصيات:\n- تحديث كلمات المرور الإدارية\n- تفعيل المصادقة الثنائية\n- مراجعة سياسات الأمان",
        affectedSystems: "خادم الويب الرئيسي، نظام إدارة المحتوى",
        responsiblePerson: "سارة أحمد - مدير الأمن السيبراني",
        directCosts: "تكلفة إصلاح الخادم وتحديث الأمان",
        indirectCosts: "توقف الخدمة لمدة 4 ساعات، فقدان ثقة العملاء",
        status: "investigating",
        createdAt: "2024-01-15T14:35:00"
    },
    {
        id: 2,
        serialNumber: "MAL-2024-002",
        title: "اكتشاف برمجية خبيثة في محطة عمل الموظف",
        severity: "high",
        type: "malware",
        date: "2024-01-14T09:15:00",
        description: "تم اكتشاف برمجية خبيثة من نوع Trojan في محطة عمل أحد الموظفين في قسم المحاسبة. البرمجية تحاول سرقة بيانات اعتماد تسجيل الدخول.",
        detailedDescription: "تفاصيل الاكتشاف:\n\n1. الجهاز المصاب: PC-ACC-05 (قسم المحاسبة)\n2. نوع البرمجية: Trojan.Win32.Stealer\n3. طريقة الدخول: مرفق بريد إلكتروني مشبوه\n4. الهدف: سرقة بيانات تسجيل الدخول\n\nالإجراءات المتخذة:\n- عزل الجهاز فوراً من الشبكة\n- فحص شامل بأدوات مكافحة الفيروسات\n- تنظيف النظام وإزالة البرمجية\n- فحص الأجهزة المجاورة\n- تغيير كلمات مرور المستخدم\n\nالدروس المستفادة:\n- ضرورة تدريب الموظفين على أمان البريد الإلكتروني\n- تحديث أنظمة الحماية\n- تطبيق سياسات أمان أكثر صرامة",
        affectedSystems: "محطة عمل PC-ACC-05",
        responsiblePerson: "محمد علي - أخصائي أمن المعلومات",
        directCosts: "تنظيف الجهاز وإعادة تثبيت النظام",
        indirectCosts: "فقدان إنتاجية الموظف لنصف يوم",
        status: "resolved",
        createdAt: "2024-01-14T09:20:00"
    },
    {
        id: 3,
        title: "رسالة تصيد إلكتروني مشبوهة",
        severity: "medium",
        type: "phishing",
        date: "2024-01-13T11:45:00",
        description: "تلقى عدة موظفين رسائل بريد إلكتروني تدعي أنها من البنك وتطلب تحديث معلومات الحساب. الرسائل تحتوي على روابط مشبوهة.",
        affectedSystems: "نظام البريد الإلكتروني",
        reportedBy: "سارة أحمد - قسم الموارد البشرية",
        status: "closed",
        createdAt: "2024-01-13T11:50:00"
    },
    {
        id: 4,
        title: "فشل في نظام النسخ الاحتياطي",
        severity: "high",
        type: "system-failure",
        date: "2024-01-12T02:00:00",
        description: "فشل نظام النسخ الاحتياطي الليلي في إكمال عملية النسخ لقاعدة البيانات الرئيسية. قد يؤثر هذا على استمرارية العمل في حالة حدوث مشكلة.",
        affectedSystems: "خادم النسخ الاحتياطي، قاعدة البيانات الرئيسية",
        reportedBy: "نظام المراقبة الآلي",
        status: "resolved",
        createdAt: "2024-01-12T02:05:00"
    },
    {
        id: 5,
        title: "وصول غير مصرح به لملفات سرية",
        severity: "critical",
        type: "unauthorized-access",
        date: "2024-01-11T16:20:00",
        description: "تم رصد محاولة وصول غير مصرح به لمجلد يحتوي على ملفات سرية من حساب مستخدم تم إيقافه مؤخراً. يبدو أن كلمة المرور لم يتم تغييرها.",
        affectedSystems: "خادم الملفات، نظام إدارة الهوية",
        reportedBy: "محمد علي - مسؤول الأمان",
        status: "investigating",
        createdAt: "2024-01-11T16:25:00"
    },
    {
        id: 6,
        serialNumber: "BREACH-2024-006",
        title: "تسريب محتمل لبيانات العملاء",
        severity: "critical",
        type: "data-breach",
        date: "2024-01-10T13:10:00",
        description: "تم اكتشاف أن قاعدة بيانات العملاء قد تكون معرضة للخطر بسبب ثغرة أمنية في تطبيق الويب. يجب التحقق من سلامة البيانات فوراً.",
        affectedSystems: "قاعدة بيانات العملاء، تطبيق الويب",
        reportedBy: "فريق الأمان الخارجي",
        responsiblePerson: "د. خالد السعيد - مستشار أمن البيانات",
        status: "open",
        createdAt: "2024-01-10T13:15:00"
    },
    {
        id: 7,
        title: "نشاط مشبوه في الشبكة الداخلية",
        severity: "medium",
        type: "intrusion",
        date: "2024-01-09T20:30:00",
        description: "تم رصد حركة بيانات غير عادية في الشبكة الداخلية خلال ساعات العمل الإضافية. قد يشير هذا إلى وجود نشاط غير مصرح به.",
        affectedSystems: "الشبكة الداخلية، جدار الحماية",
        reportedBy: "نظام مراقبة الشبكة",
        status: "investigating",
        createdAt: "2024-01-09T20:35:00"
    },
    {
        id: 8,
        title: "تحديث أمني عاجل مطلوب",
        severity: "high",
        type: "other",
        date: "2024-01-08T08:00:00",
        description: "تم الإعلان عن ثغرة أمنية خطيرة في نظام التشغيل المستخدم على الخوادم. يجب تطبيق التحديث الأمني فوراً لتجنب الاستغلال.",
        affectedSystems: "جميع الخوادم",
        reportedBy: "قسم تقنية المعلومات",
        status: "resolved",
        createdAt: "2024-01-08T08:05:00"
    },
    {
        id: 9,
        title: "محاولة تسجيل دخول مشبوهة لحساب المدير",
        severity: "high",
        type: "unauthorized-access",
        date: "2024-01-07T23:45:00",
        description: "تم رصد محاولات متعددة لتسجيل الدخول لحساب المدير من موقع جغرافي غير معتاد خارج ساعات العمل الرسمية.",
        affectedSystems: "نظام إدارة الهوية، حساب المدير",
        reportedBy: "نظام المراقبة الآلي",
        status: "closed",
        createdAt: "2024-01-07T23:50:00"
    },
    {
        id: 10,
        title: "انقطاع في خدمة البريد الإلكتروني",
        severity: "low",
        type: "system-failure",
        date: "2024-01-06T14:15:00",
        description: "انقطاع مؤقت في خدمة البريد الإلكتروني لمدة 30 دقيقة بسبب مشكلة في الخادم. تم استعادة الخدمة بنجاح.",
        affectedSystems: "خادم البريد الإلكتروني",
        reportedBy: "فريق الدعم التقني",
        status: "resolved",
        createdAt: "2024-01-06T14:20:00"
    }
];

// Function to load demo data
function loadDemoData() {
    if (confirm('هل تريد تحميل البيانات التجريبية؟ سيتم استبدال البيانات الحالية.')) {
        try {
            localStorage.setItem('securityEvents', JSON.stringify(demoEvents));
            localStorage.setItem('currentEventId', '11');

            // Update the security manager if it exists
            if (typeof securityManager !== 'undefined') {
                securityManager.events = demoEvents;
                securityManager.currentEventId = 11;
                securityManager.renderEvents();
                securityManager.updateStatistics();
                securityManager.updateCharts();
                securityManager.updateRiskAnalysis();
            } else {
                // Reload the page to show demo data
                window.location.reload();
            }
        } catch (error) {
            console.error('Error loading demo data:', error);
            alert('حدث خطأ أثناء تحميل البيانات التجريبية');
        }
    }
}

// Function to clear all data
function clearAllData() {
    if (confirm('هل أنت متأكد من حذف جميع البيانات؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        try {
            localStorage.removeItem('securityEvents');
            localStorage.removeItem('currentEventId');

            // Update the security manager if it exists
            if (typeof securityManager !== 'undefined') {
                securityManager.events = [];
                securityManager.currentEventId = 1;
                securityManager.renderEvents();
                securityManager.updateStatistics();
                securityManager.updateCharts();
                securityManager.updateRiskAnalysis();
            } else {
                // Reload the page
                window.location.reload();
            }
        } catch (error) {
            console.error('Error clearing data:', error);
            alert('حدث خطأ أثناء مسح البيانات');
        }
    }
}

// Add demo controls to the page
document.addEventListener('DOMContentLoaded', function() {
    // Create demo controls container
    const demoControls = document.createElement('div');
    demoControls.style.cssText = `
        position: fixed;
        bottom: 20px;
        left: 20px;
        background: var(--bg-primary);
        border: 2px solid var(--primary-blue);
        border-radius: 8px;
        padding: 1rem;
        box-shadow: 0 4px 20px var(--shadow);
        z-index: 1000;
        display: flex;
        gap: 0.5rem;
        flex-direction: column;
    `;
    
    
    
    // Hide demo controls on mobile
    if (window.innerWidth < 768) {
        demoControls.style.display = 'none';
    }
});
