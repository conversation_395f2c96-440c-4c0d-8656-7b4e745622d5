#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
خدمة ويندوز لنظام إدارة أمن المعلومات
Windows Service for Information Security Management System
"""

import win32serviceutil
import win32service
import win32event
import servicemanager
import socket
import sys
import os
import subprocess
import threading
import time

class ISMSWindowsService(win32serviceutil.ServiceFramework):
    _svc_name_ = "ISMSService"
    _svc_display_name_ = "Information Security Management System Service"
    _svc_description_ = "خدمة نظام إدارة أمن المعلومات - يقوم بتشغيل خادم الويب تلقائياً"
    
    def __init__(self, args):
        win32serviceutil.ServiceFramework.__init__(self, args)
        self.hWaitStop = win32event.CreateEvent(None, 0, 0, None)
        self.server_process = None
        self.is_running = False
        
    def SvcStop(self):
        """إيقاف الخدمة"""
        self.ReportServiceStatus(win32service.SERVICE_STOP_PENDING)
        win32event.SetEvent(self.hWaitStop)
        self.is_running = False
        
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
            except:
                try:
                    self.server_process.kill()
                except:
                    pass
        
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STOPPED,
            (self._svc_name_, '')
        )
    
    def SvcDoRun(self):
        """تشغيل الخدمة"""
        servicemanager.LogMsg(
            servicemanager.EVENTLOG_INFORMATION_TYPE,
            servicemanager.PYS_SERVICE_STARTED,
            (self._svc_name_, '')
        )
        
        self.is_running = True
        self.main()
    
    def main(self):
        """الدالة الرئيسية للخدمة"""
        try:
            # تغيير مجلد العمل
            service_dir = os.path.dirname(os.path.abspath(__file__))
            os.chdir(service_dir)
            
            # تشغيل الخادم
            self.start_server()
            
            # انتظار إشارة الإيقاف
            while self.is_running:
                rc = win32event.WaitForSingleObject(self.hWaitStop, 1000)
                if rc == win32event.WAIT_OBJECT_0:
                    break
                
                # التحقق من حالة الخادم
                if self.server_process and self.server_process.poll() is not None:
                    # الخادم توقف، إعادة تشغيله
                    servicemanager.LogMsg(
                        servicemanager.EVENTLOG_WARNING_TYPE,
                        servicemanager.PYS_SERVICE_STOPPED,
                        (self._svc_name_, 'Server stopped unexpectedly, restarting...')
                    )
                    time.sleep(5)
                    self.start_server()
                    
        except Exception as e:
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_ERROR_TYPE,
                servicemanager.PYS_SERVICE_STOPPED,
                (self._svc_name_, f'Service error: {str(e)}')
            )
    
    def start_server(self):
        """تشغيل خادم الويب"""
        try:
            # البحث عن Python
            python_exe = sys.executable
            server_script = os.path.join(os.getcwd(), 'start-server.py')
            
            if not os.path.exists(server_script):
                raise FileNotFoundError(f"Server script not found: {server_script}")
            
            # تشغيل الخادم
            self.server_process = subprocess.Popen(
                [python_exe, server_script],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.getcwd()
            )
            
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_INFORMATION_TYPE,
                servicemanager.PYS_SERVICE_STARTED,
                (self._svc_name_, f'Server started with PID: {self.server_process.pid}')
            )
            
        except Exception as e:
            servicemanager.LogMsg(
                servicemanager.EVENTLOG_ERROR_TYPE,
                servicemanager.PYS_SERVICE_STOPPED,
                (self._svc_name_, f'Failed to start server: {str(e)}')
            )

def install_service():
    """تثبيت الخدمة"""
    try:
        win32serviceutil.InstallService(
            ISMSWindowsService._svc_reg_class_,
            ISMSWindowsService._svc_name_,
            ISMSWindowsService._svc_display_name_,
            description=ISMSWindowsService._svc_description_
        )
        print("✅ تم تثبيت الخدمة بنجاح")
        print("🔧 لتشغيل الخدمة: net start ISMSService")
        return True
    except Exception as e:
        print(f"❌ فشل في تثبيت الخدمة: {e}")
        return False

def uninstall_service():
    """إلغاء تثبيت الخدمة"""
    try:
        win32serviceutil.RemoveService(ISMSWindowsService._svc_name_)
        print("✅ تم إلغاء تثبيت الخدمة بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في إلغاء تثبيت الخدمة: {e}")
        return False

def start_service():
    """تشغيل الخدمة"""
    try:
        win32serviceutil.StartService(ISMSWindowsService._svc_name_)
        print("✅ تم تشغيل الخدمة بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في تشغيل الخدمة: {e}")
        return False

def stop_service():
    """إيقاف الخدمة"""
    try:
        win32serviceutil.StopService(ISMSWindowsService._svc_name_)
        print("✅ تم إيقاف الخدمة بنجاح")
        return True
    except Exception as e:
        print(f"❌ فشل في إيقاف الخدمة: {e}")
        return False

def service_status():
    """عرض حالة الخدمة"""
    try:
        status = win32serviceutil.QueryServiceStatus(ISMSWindowsService._svc_name_)
        status_map = {
            win32service.SERVICE_STOPPED: "متوقفة",
            win32service.SERVICE_START_PENDING: "جاري التشغيل",
            win32service.SERVICE_STOP_PENDING: "جاري الإيقاف",
            win32service.SERVICE_RUNNING: "تعمل",
            win32service.SERVICE_CONTINUE_PENDING: "جاري الاستكمال",
            win32service.SERVICE_PAUSE_PENDING: "جاري الإيقاف المؤقت",
            win32service.SERVICE_PAUSED: "متوقفة مؤقتاً"
        }
        
        current_status = status_map.get(status[1], "غير معروف")
        print(f"📊 حالة الخدمة: {current_status}")
        return status[1]
    except Exception as e:
        print(f"❌ فشل في الحصول على حالة الخدمة: {e}")
        return None

def main():
    """الدالة الرئيسية"""
    if len(sys.argv) == 1:
        # تشغيل كخدمة
        servicemanager.Initialize()
        servicemanager.PrepareToHostSingle(ISMSWindowsService)
        servicemanager.StartServiceCtrlDispatcher()
    else:
        # تشغيل من سطر الأوامر
        command = sys.argv[1].lower()
        
        if command == 'install':
            install_service()
        elif command == 'uninstall':
            uninstall_service()
        elif command == 'start':
            start_service()
        elif command == 'stop':
            stop_service()
        elif command == 'status':
            service_status()
        elif command == 'restart':
            stop_service()
            time.sleep(2)
            start_service()
        else:
            print("🔧 استخدام:")
            print("  python windows-service.py install   - تثبيت الخدمة")
            print("  python windows-service.py uninstall - إلغاء تثبيت الخدمة")
            print("  python windows-service.py start     - تشغيل الخدمة")
            print("  python windows-service.py stop      - إيقاف الخدمة")
            print("  python windows-service.py restart   - إعادة تشغيل الخدمة")
            print("  python windows-service.py status    - عرض حالة الخدمة")

if __name__ == '__main__':
    main()
