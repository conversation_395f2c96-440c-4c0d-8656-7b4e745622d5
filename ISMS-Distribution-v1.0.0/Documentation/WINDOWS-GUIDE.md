# 🪟 دليل ويندوز - نظام إدارة أمن المعلومات
# Windows Guide - Information Security Management System

## 🎯 الطرق المتاحة لتشغيل النظام على ويندوز

### 1. 🖥️ تطبيق ويندوز (موصى به)
تطبيق بواجهة رسومية سهل الاستخدام

**المميزات:**
- واجهة رسومية باللغة العربية
- تحكم كامل في الخادم
- عرض سجل الأحداث
- نسخ الروابط بسهولة
- إعدادات قابلة للحفظ

**التشغيل:**
```bash
python windows-app.py
```

### 2. 🌐 خادم ويب تقليدي
الطريقة الأصلية عبر المتصفح

**التشغيل:**
```bash
python start-server.py
```

### 3. 🔧 خدمة ويندوز
تشغيل النظام كخدمة في الخلفية

**التثبيت:**
```bash
python windows-service.py install
python windows-service.py start
```

### 4. 📦 ملف تنفيذي مستقل
تطبيق لا يحتاج Python

**الإنشاء:**
```bash
python build-exe.py
```

## 🚀 التثبيت السريع

### الطريقة الأولى: التثبيت التلقائي
```batch
# انقر نقراً مزدوجاً على
install.bat
```

### الطريقة الثانية: التثبيت اليدوي
```bash
# 1. تأكد من تثبيت Python
python --version

# 2. شغل تطبيق ويندوز
python windows-app.py
```

## 🖥️ تطبيق ويندوز - الدليل المفصل

### واجهة التطبيق:

#### 📊 معلومات الخادم
- **حالة الخادم:** 🟢 يعمل / 🔴 متوقف
- **عنوان IP:** يتم اكتشافه تلقائياً
- **المنفذ:** قابل للتعديل (افتراضي: 8000)

#### 🎛️ أزرار التحكم
- **🚀 تشغيل الخادم:** بدء تشغيل النظام
- **⏹️ إيقاف الخادم:** إيقاف النظام
- **🌐 فتح المتصفح:** فتح النظام في المتصفح

#### 🔗 روابط الوصول
- **المحلي:** للوصول من نفس الجهاز
- **الشبكة:** للوصول من أجهزة أخرى
- **زر نسخ:** لنسخ الرابط للحافظة

#### 🔑 بيانات تسجيل الدخول
- **المدير:** admin / admin123
- **المحلل:** analyst / analyst123
- **المشغل:** operator / operator123

#### 📝 سجل الأحداث
- عرض جميع أنشطة النظام
- رسائل الحالة والأخطاء
- طوابع زمنية لكل حدث

### ميزات متقدمة:

#### 🔍 اكتشاف المنفذ التلقائي
- البحث عن منفذ متاح تلقائياً
- تحذير عند استخدام منفذ مشغول
- اقتراح منفذ بديل

#### 💾 حفظ الإعدادات
- حفظ المنفذ المفضل
- استرجاع الإعدادات عند التشغيل
- ملف إعدادات: `windows-app-settings.json`

#### 🛡️ الأمان
- إيقاف آمن للخادم
- تأكيد قبل الإغلاق
- حماية من فقدان البيانات

## 🔧 خدمة ويندوز - الدليل المفصل

### تثبيت الخدمة:
```bash
# تثبيت
python windows-service.py install

# تشغيل
python windows-service.py start

# التحقق من الحالة
python windows-service.py status
```

### إدارة الخدمة:
```bash
# إيقاف
python windows-service.py stop

# إعادة تشغيل
python windows-service.py restart

# إلغاء التثبيت
python windows-service.py uninstall
```

### مميزات الخدمة:
- **تشغيل تلقائي:** مع بدء تشغيل ويندوز
- **إعادة تشغيل تلقائي:** عند توقف الخادم
- **تسجيل الأحداث:** في سجل أحداث ويندوز
- **عمل في الخلفية:** بدون واجهة مستخدم

## 📦 إنشاء ملف تنفيذي

### المتطلبات:
```bash
pip install pyinstaller
```

### الإنشاء:
```bash
python build-exe.py
```

### النتائج:
- **الملف التنفيذي:** `dist/ISMS-Windows.exe`
- **ملف المثبت:** `isms-installer.iss`

### المميزات:
- **مستقل:** لا يحتاج Python
- **محمول:** يعمل على أي جهاز ويندوز
- **أيقونة مخصصة:** شعار النظام
- **مثبت احترافي:** باستخدام Inno Setup

## 🛠️ استكشاف الأخطاء

### مشكلة: Python غير مثبت
**الحل:**
1. تحميل Python من [python.org](https://python.org)
2. تأكد من تفعيل "Add to PATH"
3. إعادة تشغيل Command Prompt

### مشكلة: المنفذ مستخدم
**الحل:**
1. تغيير المنفذ في التطبيق
2. أو إيقاف البرنامج المستخدم للمنفذ:
```bash
netstat -ano | findstr :8000
taskkill /PID [رقم_العملية] /F
```

### مشكلة: جدار الحماية
**الحل:**
```bash
# السماح بالمنفذ
netsh advfirewall firewall add rule name="ISMS" dir=in action=allow protocol=TCP localport=8000
```

### مشكلة: خطأ في الخدمة
**الحل:**
1. تشغيل Command Prompt كمدير
2. التأكد من صلاحيات المجلد
3. فحص سجل أحداث ويندوز

## 📋 قائمة المراجعة

### ✅ قبل التشغيل:
- [ ] Python مثبت (3.7 أو أحدث)
- [ ] جميع الملفات موجودة
- [ ] المنفذ 8000 متاح
- [ ] جدار الحماية مضبوط

### ✅ بعد التشغيل:
- [ ] الخادم يعمل بنجاح
- [ ] يمكن الوصول محلياً
- [ ] يمكن الوصول من الشبكة
- [ ] تسجيل الدخول يعمل

## 🎯 التوصيات

### للاستخدام الشخصي:
- استخدم **تطبيق ويندوز** للسهولة

### للخوادم:
- استخدم **خدمة ويندوز** للتشغيل المستمر

### للتوزيع:
- أنشئ **ملف تنفيذي** للمحمولية

### للتطوير:
- استخدم **الخادم التقليدي** للمرونة

## 📞 الدعم الفني

### الملفات المساعدة:
- `SYSTEM-STATUS.md` - حالة النظام
- `LOGIN-GUIDE.md` - مشاكل تسجيل الدخول
- `NETWORK-SETUP.md` - إعداد الشبكة

### الأوامر المفيدة:
```bash
# فحص Python
python --version

# فحص المنافذ
netstat -an | findstr :8000

# فحص العمليات
tasklist | findstr python

# فحص الخدمات
sc query ISMSService
```

---

**🪟 النظام محسن بالكامل لويندوز!**  
**جرب جميع الطرق واختر الأنسب لاحتياجاتك**
